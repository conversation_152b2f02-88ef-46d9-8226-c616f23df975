"""
Optimized SAITS implementation with GPU acceleration and performance improvements.
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, RegressorMixin
import warnings
warnings.filterwarnings('ignore')


class OptimizedSAITSRegressor(BaseEstimator, RegressorMixin):
    """
    Optimized SAITS Regressor with GPU acceleration and performance improvements.
    """

    def __init__(self, sequence_length=50, n_groups=1, n_group_inner_layers=1,
                 d_model=64, d_inner=128, n_head=4, d_k=16, d_v=16, dropout=0.1,
                 epochs=100, batch_size=32, learning_rate=0.001, patience=10,
                 input_with_mask=True, param_sharing_strategy="inner_group",
                 MIT=False, random_state=42, 
                 # New optimization parameters
                 use_amp=True, compile_model=True, num_workers=4,
                 gradient_accumulation_steps=1, **kwargs):
        
        # Original parameters
        self.sequence_length = sequence_length
        self.n_groups = n_groups
        self.n_group_inner_layers = n_group_inner_layers
        self.d_model = d_model
        self.d_inner = d_inner
        self.n_head = n_head
        self.d_k = d_k
        self.d_v = d_v
        self.dropout = dropout
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.patience = patience
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT
        self.random_state = random_state
        
        # New optimization parameters
        self.use_amp = use_amp and torch.cuda.is_available()
        self.compile_model = compile_model
        self.num_workers = num_workers
        self.gradient_accumulation_steps = gradient_accumulation_steps

        # Set device with optimization
        self.device = self._get_optimal_device()
        
        # Initialize components
        self.model = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        
        # AMP scaler for mixed precision
        self.amp_scaler = torch.cuda.amp.GradScaler() if self.use_amp else None
        
        print(f"🚀 OptimizedSAITS initialized:")
        print(f"   Device: {self.device}")
        print(f"   Mixed Precision: {self.use_amp}")
        print(f"   Model Compilation: {self.compile_model}")

    def _get_optimal_device(self):
        """Get the optimal device for computation."""
        if torch.cuda.is_available():
            # Use the GPU with most free memory
            gpu_count = torch.cuda.device_count()
            if gpu_count > 1:
                max_memory = 0
                best_gpu = 0
                for i in range(gpu_count):
                    torch.cuda.set_device(i)
                    free_memory = torch.cuda.get_device_properties(i).total_memory - torch.cuda.memory_allocated()
                    if free_memory > max_memory:
                        max_memory = free_memory
                        best_gpu = i
                return torch.device(f'cuda:{best_gpu}')
            else:
                return torch.device('cuda:0')
        else:
            return torch.device('cpu')

    def _create_optimized_dataloader(self, X_seq, y_seq=None, mask_seq=None, shuffle=True):
        """Create optimized DataLoader with proper batching."""
        if y_seq is not None and mask_seq is not None:
            dataset = torch.utils.data.TensorDataset(
                torch.FloatTensor(X_seq),
                torch.FloatTensor(y_seq),
                torch.FloatTensor(mask_seq)
            )
        else:
            dataset = torch.utils.data.TensorDataset(torch.FloatTensor(X_seq))
        
        return torch.utils.data.DataLoader(
            dataset,
            batch_size=self.batch_size,
            shuffle=shuffle,
            num_workers=self.num_workers if self.device.type == 'cuda' else 0,
            pin_memory=True if self.device.type == 'cuda' else False,
            persistent_workers=True if self.num_workers > 0 else False
        )

    def fit(self, X, y, eval_set=None, verbose=False):
        """Fit the optimized SAITS model with GPU acceleration."""
        self.feature_names_ = getattr(X, 'columns', None)

        # Prepare data (same as original)
        X_orig = np.array(X)
        y_orig = np.array(y)
        X_prep, y_prep = self._prepare_data(X, y, fit_scalers=True)
        X_seq, y_seq, combined_mask = self._create_sequences(X_prep, y_prep, X_orig, y_orig)
        
        # Combine features and target
        combined_data = np.concatenate([X_seq, y_seq.reshape(y_seq.shape[0], y_seq.shape[1], 1)], axis=2)
        
        # Initialize model
        d_feature = combined_data.shape[2]
        self.model = OptimizedSAITS(
            n_groups=self.n_groups,
            n_group_inner_layers=self.n_group_inner_layers,
            d_time=self.sequence_length,
            d_feature=d_feature,
            d_model=self.d_model,
            d_inner=self.d_inner,
            n_head=self.n_head,
            d_k=self.d_k,
            d_v=self.d_v,
            dropout=self.dropout,
            input_with_mask=self.input_with_mask,
            param_sharing_strategy=self.param_sharing_strategy,
            MIT=self.MIT,
            device=self.device
        ).to(self.device)
        
        # Compile model for optimization (PyTorch 2.0+)
        if self.compile_model and hasattr(torch, 'compile'):
            try:
                # Check if triton is available before compiling
                try:
                    import triton
                    self.model = torch.compile(self.model, mode='reduce-overhead')
                    print("✅ Model compiled for optimization")
                except ImportError:
                    print("⚠️  Triton not available, skipping torch.compile()")
                    print("💡 Install triton for additional GPU optimization: pip install triton")
                except Exception as compile_error:
                    print(f"⚠️  Model compilation failed: {compile_error}")
                    print("💡 Continuing without torch.compile() optimization")
            except Exception as e:
                print(f"⚠️  Model compilation setup failed: {e}")

        # Create optimized DataLoader
        dataloader = self._create_optimized_dataloader(combined_data, combined_mask, shuffle=True)
        
        # Setup optimizer
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=self.learning_rate, weight_decay=1e-5)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=self.patience//2, factor=0.5)
        
        # Training loop with optimizations
        best_loss = float('inf')
        patience_counter = 0
        
        print(f"🏋️  Training with {len(dataloader)} batches per epoch...")
        
        for epoch in range(self.epochs):
            self.model.train()
            epoch_loss = 0
            num_batches = 0
            
            # Gradient accumulation
            optimizer.zero_grad()
            
            for batch_idx, batch in enumerate(dataloader):
                if len(batch) == 3:
                    batch_data, batch_mask, _ = batch
                else:
                    batch_data, batch_mask = batch[0], batch[1]
                
                batch_data = batch_data.to(self.device, non_blocking=True)
                batch_mask = batch_mask.to(self.device, non_blocking=True)
                
                # Mixed precision training
                if self.use_amp:
                    with torch.cuda.amp.autocast():
                        inputs = {"X": batch_data, "missing_mask": batch_mask}
                        outputs = self.model(inputs)
                        loss = outputs["reconstruction_loss"]
                        loss = loss / self.gradient_accumulation_steps
                    
                    self.amp_scaler.scale(loss).backward()
                    
                    if (batch_idx + 1) % self.gradient_accumulation_steps == 0:
                        self.amp_scaler.step(optimizer)
                        self.amp_scaler.update()
                        optimizer.zero_grad()
                else:
                    inputs = {"X": batch_data, "missing_mask": batch_mask}
                    outputs = self.model(inputs)
                    loss = outputs["reconstruction_loss"]
                    loss = loss / self.gradient_accumulation_steps
                    loss.backward()
                    
                    if (batch_idx + 1) % self.gradient_accumulation_steps == 0:
                        optimizer.step()
                        optimizer.zero_grad()
                
                epoch_loss += loss.item() * self.gradient_accumulation_steps
                num_batches += 1
            
            avg_loss = epoch_loss / num_batches
            scheduler.step(avg_loss)
            
            if verbose and epoch % 10 == 0:
                print(f"Epoch {epoch:3d}/{self.epochs}: Loss = {avg_loss:.6f}")
            
            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= self.patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
        
        print(f"✅ Training completed. Best loss: {best_loss:.6f}")
        return self

    def predict(self, X):
        """Predict using the optimized SAITS model."""
        if self.model is None:
            raise ValueError("Model must be fitted before prediction")

        # Prepare data (same as original)
        X_orig = np.array(X)
        X_prep = self._prepare_data(X, fit_scalers=False)
        
        dummy_target = np.zeros(len(X_prep))
        dummy_target_orig = np.full(len(X_prep), np.nan)
        
        X_seq, mask_seq = self._create_sequences(
            np.concatenate([X_prep, dummy_target.reshape(-1, 1)], axis=1),
            original_X=np.concatenate([X_orig, dummy_target_orig.reshape(-1, 1)], axis=1)
        )
        
        # Create optimized DataLoader for prediction
        dataloader = self._create_optimized_dataloader(X_seq, shuffle=False)
        
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for batch in dataloader:
                batch_data = batch[0].to(self.device, non_blocking=True)
                batch_mask = torch.ones_like(batch_data).to(self.device)
                batch_mask[:, :, -1] = 0  # Mask the target column
                
                if self.use_amp:
                    with torch.cuda.amp.autocast():
                        inputs = {"X": batch_data, "missing_mask": batch_mask}
                        outputs = self.model.impute(inputs)
                        batch_preds = outputs[:, -1, -1].cpu().numpy()
                else:
                    inputs = {"X": batch_data, "missing_mask": batch_mask}
                    outputs = self.model.impute(inputs)
                    batch_preds = outputs[:, -1, -1].cpu().numpy()
                
                predictions.extend(batch_preds)
        
        predictions = np.array(predictions[:len(X)])
        return self.scaler_y.inverse_transform(predictions.reshape(-1, 1)).flatten()

    # Include the same helper methods from original implementation
    def _prepare_data(self, X, y=None, fit_scalers=False):
        """Same as original implementation"""
        # [Implementation details same as original]
        pass
    
    def _create_sequences(self, X, y=None, original_X=None, original_y=None):
        """Same as original implementation"""
        # [Implementation details same as original]
        pass


class OptimizedSAITS(nn.Module):
    """Optimized SAITS model with performance improvements."""
    
    def __init__(self, n_groups=1, n_group_inner_layers=1, d_time=100, d_feature=4, 
                 d_model=64, d_inner=128, n_head=4, d_k=16, d_v=16, dropout=0.1, 
                 input_with_mask=True, param_sharing_strategy="inner_group", 
                 MIT=False, device='cpu', **kwargs):
        super().__init__()
        
        # Same initialization as original
        self.n_groups = n_groups
        self.n_group_inner_layers = n_group_inner_layers
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT
        self.device = device
        
        actual_d_feature = d_feature * 2 if self.input_with_mask else d_feature
        
        # Use optimized attention if available
        try:
            from torch.nn.functional import scaled_dot_product_attention
            self.use_optimized_attention = True
        except ImportError:
            self.use_optimized_attention = False
        
        # Initialize layers with better initialization
        self.embedding_1 = nn.Linear(actual_d_feature, d_model)
        self.position_enc = PositionalEncoding(d_model, max_len=d_time)
        self.dropout = nn.Dropout(dropout)
        
        # Create attention layers (simplified for brevity)
        # [Rest of the implementation would follow the original structure]
        # but with optimizations like:
        # - Better weight initialization
        # - Optimized attention mechanisms
        # - Memory-efficient operations
        
    def forward(self, inputs, stage="train"):
        """Forward pass with optimizations."""
        # Implementation with optimizations
        pass


class PositionalEncoding(nn.Module):
    """Optimized positional encoding."""
    
    def __init__(self, d_model, max_len=5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)

    def forward(self, x):
        return x + self.pe[:x.size(1), :].transpose(0, 1)
