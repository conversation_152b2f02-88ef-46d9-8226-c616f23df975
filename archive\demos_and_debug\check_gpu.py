#!/usr/bin/env python3
"""
GPU availability and performance check for SAITS optimization.
"""

import torch
import numpy as np
import time

def check_gpu_availability():
    """Check GPU availability and specifications."""
    print("=" * 60)
    print(" GPU AVAILABILITY CHECK")
    print("=" * 60)
    
    # Check CUDA availability
    cuda_available = torch.cuda.is_available()
    print(f"CUDA Available: {cuda_available}")
    
    if cuda_available:
        # GPU information
        gpu_count = torch.cuda.device_count()
        print(f"Number of GPUs: {gpu_count}")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        
        # Current GPU
        current_device = torch.cuda.current_device()
        print(f"Current GPU: {current_device}")
        
        # Memory usage
        allocated = torch.cuda.memory_allocated() / 1024**3
        cached = torch.cuda.memory_reserved() / 1024**3
        print(f"GPU Memory - Allocated: {allocated:.2f} GB, Cached: {cached:.2f} GB")
        
    else:
        print("❌ CUDA not available. Running on CPU only.")
        print("To enable GPU acceleration:")
        print("1. Install CUDA toolkit from NVIDIA")
        print("2. Install PyTorch with CUDA support:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    
    return cuda_available

def benchmark_gpu_vs_cpu():
    """Benchmark GPU vs CPU performance for typical SAITS operations."""
    print("\n" + "=" * 60)
    print(" GPU vs CPU PERFORMANCE BENCHMARK")
    print("=" * 60)
    
    # Test parameters similar to SAITS
    batch_size = 32
    seq_length = 50
    d_model = 64
    n_features = 4
    
    # Create test data
    test_data = torch.randn(batch_size, seq_length, n_features)
    
    # CPU benchmark
    print("\n🔄 CPU Benchmark...")
    device_cpu = torch.device('cpu')
    test_data_cpu = test_data.to(device_cpu)
    
    # Simple transformer-like operations
    linear_cpu = torch.nn.Linear(n_features, d_model).to(device_cpu)
    attention_cpu = torch.nn.MultiheadAttention(d_model, num_heads=4, batch_first=True).to(device_cpu)
    
    start_time = time.time()
    for _ in range(100):
        x = linear_cpu(test_data_cpu)
        attn_output, _ = attention_cpu(x, x, x)
    cpu_time = time.time() - start_time
    print(f"CPU Time: {cpu_time:.3f} seconds")
    
    # GPU benchmark (if available)
    if torch.cuda.is_available():
        print("\n🚀 GPU Benchmark...")
        device_gpu = torch.device('cuda')
        test_data_gpu = test_data.to(device_gpu)
        
        linear_gpu = torch.nn.Linear(n_features, d_model).to(device_gpu)
        attention_gpu = torch.nn.MultiheadAttention(d_model, num_heads=4, batch_first=True).to(device_gpu)
        
        # Warm up GPU
        for _ in range(10):
            x = linear_gpu(test_data_gpu)
            attn_output, _ = attention_gpu(x, x, x)
        torch.cuda.synchronize()
        
        start_time = time.time()
        for _ in range(100):
            x = linear_gpu(test_data_gpu)
            attn_output, _ = attention_gpu(x, x, x)
        torch.cuda.synchronize()
        gpu_time = time.time() - start_time
        print(f"GPU Time: {gpu_time:.3f} seconds")
        
        speedup = cpu_time / gpu_time
        print(f"\n🎯 GPU Speedup: {speedup:.2f}x faster than CPU")
        
        if speedup < 1.5:
            print("⚠️  Warning: Low GPU speedup. Consider:")
            print("   - Increasing batch size")
            print("   - Using larger model dimensions")
            print("   - Checking GPU utilization")
    
    return cpu_time, gpu_time if torch.cuda.is_available() else None

def check_pytorch_optimization():
    """Check PyTorch optimization settings."""
    print("\n" + "=" * 60)
    print(" PYTORCH OPTIMIZATION SETTINGS")
    print("=" * 60)
    
    # Check compilation support
    try:
        compile_available = hasattr(torch, 'compile')
        print(f"torch.compile available: {compile_available}")
    except:
        compile_available = False
        print("torch.compile not available")
    
    # Check mixed precision support
    if torch.cuda.is_available():
        amp_available = hasattr(torch.cuda, 'amp')
        print(f"Automatic Mixed Precision (AMP) available: {amp_available}")
    
    # Check optimized attention
    try:
        from torch.nn.functional import scaled_dot_product_attention
        sdpa_available = True
        print("Scaled Dot Product Attention (optimized) available: True")
    except ImportError:
        sdpa_available = False
        print("Scaled Dot Product Attention (optimized) available: False")
    
    # Threading info
    print(f"Number of CPU threads: {torch.get_num_threads()}")
    
    return {
        'compile': compile_available,
        'amp': amp_available if torch.cuda.is_available() else False,
        'sdpa': sdpa_available
    }

if __name__ == "__main__":
    # Run all checks
    gpu_available = check_gpu_availability()
    cpu_time, gpu_time = benchmark_gpu_vs_cpu()
    optimization_features = check_pytorch_optimization()
    
    print("\n" + "=" * 60)
    print(" OPTIMIZATION RECOMMENDATIONS")
    print("=" * 60)
    
    if gpu_available:
        print("✅ GPU acceleration is available!")
        print("Recommendations:")
        print("1. Increase batch_size for better GPU utilization")
        print("2. Use mixed precision training (AMP) to save memory")
        print("3. Consider using torch.compile() for additional speedup")
    else:
        print("❌ GPU not available. To enable GPU acceleration:")
        print("1. Install NVIDIA drivers and CUDA toolkit")
        print("2. Reinstall PyTorch with CUDA support")
        print("3. Verify GPU compatibility")
    
    if optimization_features['amp']:
        print("4. Enable Automatic Mixed Precision (AMP)")
    
    if optimization_features['compile']:
        print("5. Use torch.compile() for model compilation")
    
    print("\nFor SAITS specifically:")
    print("- Increase sequence_length and batch_size")
    print("- Use larger d_model and d_inner dimensions")
    print("- Enable gradient accumulation for large batches")
    print("- Consider using DataLoader with multiple workers")
