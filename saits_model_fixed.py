#!/usr/bin/env python3
"""
Fixed SAITS Model Implementation
===============================

This module provides a numerically stable SAITS implementation that fixes the "Input contains NaN" error.
Key improvements:
1. Comprehensive NaN detection and handling
2. Numerical stability improvements
3. Robust error handling
4. Input validation
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, RegressorMixin
import warnings
warnings.filterwarnings('ignore')

def debug_tensor(tensor, name, step="", verbose=False):
    """Debug function to check tensor properties and NaN values."""
    if torch.is_tensor(tensor):
        has_nan = torch.isnan(tensor).any().item()
        has_inf = torch.isinf(tensor).any().item()
        if has_nan or has_inf or verbose:
            min_val = tensor.min().item() if tensor.numel() > 0 else 0
            max_val = tensor.max().item() if tensor.numel() > 0 else 0
            print(f"🔍 {step} {name}: shape={tensor.shape}, NaN={has_nan}, Inf={has_inf}, "
                  f"min={min_val:.6f}, max={max_val:.6f}")
            if has_nan or has_inf:
                return False
    return True

def safe_tensor_operation(tensor, operation_name="operation"):
    """Safely handle tensor operations with NaN/Inf checking."""
    if torch.is_tensor(tensor):
        if torch.isnan(tensor).any() or torch.isinf(tensor).any():
            print(f"⚠️  Cleaning {operation_name}: NaN/Inf detected")
            tensor = torch.nan_to_num(tensor, nan=0.0, posinf=1e6, neginf=-1e6)
        # Clamp to reasonable range
        tensor = torch.clamp(tensor, -1e6, 1e6)
    return tensor

class StablePositionalEncoding(nn.Module):
    """Numerically stable positional encoding."""
    
    def __init__(self, d_model, n_position=200):
        super().__init__()
        self.d_model = max(d_model, 1)  # Prevent division by zero
        
        # Create positional encoding with numerical stability
        pe = torch.zeros(n_position, self.d_model)
        position = torch.arange(0, n_position, dtype=torch.float).unsqueeze(1)
        
        # Prevent division by zero and use stable computation
        if self.d_model > 1:
            div_term = torch.exp(torch.arange(0, self.d_model, 2).float() * 
                               (-np.log(10000.0) / self.d_model))
            
            pe[:, 0::2] = torch.sin(position * div_term)
            if self.d_model > 1:
                pe[:, 1::2] = torch.cos(position * div_term[:pe[:, 1::2].shape[1]])
        else:
            # Handle edge case where d_model = 1
            pe[:, 0] = torch.sin(position.squeeze())
        
        # Clamp to prevent extreme values
        pe = torch.clamp(pe, -10.0, 10.0)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        debug_tensor(x, "PositionalEncoding input", "PE")
        
        # Clean input
        x = safe_tensor_operation(x, "PE input")
        
        seq_len = x.size(1)
        
        # Ensure we don't exceed buffer
        if seq_len > self.pe.size(0):
            print(f"⚠️  Sequence length {seq_len} exceeds PE buffer {self.pe.size(0)}")
            seq_len = min(seq_len, self.pe.size(0))
        
        pe_slice = self.pe[:seq_len, :].unsqueeze(0)
        output = x + pe_slice
        
        # Clean output
        output = safe_tensor_operation(output, "PE output")
        
        debug_tensor(output, "PositionalEncoding output", "PE")
        return output

class StableScaledDotProductAttention(nn.Module):
    """Numerically stable scaled dot-product attention."""
    
    def __init__(self, temperature, attn_dropout=0.1):
        super().__init__()
        self.temperature = max(temperature, 1e-6)  # Prevent division by zero
        self.dropout = nn.Dropout(attn_dropout)
    
    def forward(self, q, k, v, mask=None):
        # Clean inputs
        q = safe_tensor_operation(q, "attention q")
        k = safe_tensor_operation(k, "attention k") 
        v = safe_tensor_operation(v, "attention v")
        
        # Compute attention with numerical stability
        attn = torch.matmul(q / self.temperature, k.transpose(2, 3))
        attn = safe_tensor_operation(attn, "attention scores")
        
        # Clamp before softmax to prevent overflow
        attn = torch.clamp(attn, -50.0, 50.0)
        
        if mask is not None:
            mask = safe_tensor_operation(mask, "attention mask")
            attn = attn.masked_fill(mask == 0, -1e9)
        
        # Stable softmax
        attn = F.softmax(attn, dim=-1)
        attn = torch.clamp(attn, 1e-8, 1.0)  # Ensure valid probabilities
        attn = self.dropout(attn)
        
        output = torch.matmul(attn, v)
        output = safe_tensor_operation(output, "attention output")
        
        return output, attn

class StableMultiHeadAttention(nn.Module):
    """Numerically stable multi-head attention."""
    
    def __init__(self, d_model, n_head, d_k, d_v, dropout=0.1):
        super().__init__()
        self.n_head = n_head
        self.d_k = max(d_k, 1)
        self.d_v = max(d_v, 1)
        
        self.w_qs = nn.Linear(d_model, n_head * self.d_k, bias=False)
        self.w_ks = nn.Linear(d_model, n_head * self.d_k, bias=False)
        self.w_vs = nn.Linear(d_model, n_head * self.d_v, bias=False)
        self.fc = nn.Linear(n_head * self.d_v, d_model, bias=False)
        
        self.attention = StableScaledDotProductAttention(temperature=max(self.d_k ** 0.5, 1e-6))
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)
        
        # Initialize weights properly
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights to prevent NaN."""
        for module in [self.w_qs, self.w_ks, self.w_vs, self.fc]:
            nn.init.xavier_uniform_(module.weight, gain=0.1)
    
    def forward(self, q, k, v, mask=None):
        debug_tensor(q, "MHA input", "MHA")
        
        # Clean inputs
        q = safe_tensor_operation(q, "MHA q")
        k = safe_tensor_operation(k, "MHA k")
        v = safe_tensor_operation(v, "MHA v")
        
        sz_b, len_q, len_k, len_v = q.size(0), q.size(1), k.size(1), v.size(1)
        residual = q.clone()
        
        # Linear transformations
        q = self.w_qs(q).view(sz_b, len_q, self.n_head, self.d_k)
        k = self.w_ks(k).view(sz_b, len_k, self.n_head, self.d_k)
        v = self.w_vs(v).view(sz_b, len_v, self.n_head, self.d_v)
        
        # Clean after linear transformations
        q = safe_tensor_operation(q, "MHA q after linear")
        k = safe_tensor_operation(k, "MHA k after linear")
        v = safe_tensor_operation(v, "MHA v after linear")
        
        # Transpose for attention
        q, k, v = q.transpose(1, 2), k.transpose(1, 2), v.transpose(1, 2)
        
        if mask is not None:
            mask = mask.unsqueeze(1).repeat(1, self.n_head, 1, 1)
        
        q, attn = self.attention(q, k, v, mask=mask)
        
        # Transpose back
        q = q.transpose(1, 2).contiguous().view(sz_b, len_q, -1)
        
        q = self.dropout(self.fc(q))
        q = safe_tensor_operation(q, "MHA after fc")
        
        q += residual
        
        # Safe layer normalization
        try:
            q = self.layer_norm(q)
            q = safe_tensor_operation(q, "MHA after layer_norm")
        except Exception as e:
            print(f"⚠️  LayerNorm failed: {e}, using manual normalization")
            mean = q.mean(dim=-1, keepdim=True)
            var = q.var(dim=-1, keepdim=True, unbiased=False)
            q = (q - mean) / torch.sqrt(var + 1e-6)
            q = safe_tensor_operation(q, "MHA after manual norm")
        
        debug_tensor(q, "MHA output", "MHA")
        return q, attn

class StablePositionwiseFeedForward(nn.Module):
    """Numerically stable position-wise feed forward network."""
    
    def __init__(self, d_model, d_inner, dropout=0.1):
        super().__init__()
        self.w_1 = nn.Linear(d_model, d_inner)
        self.w_2 = nn.Linear(d_inner, d_model)
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)
        self.dropout = nn.Dropout(dropout)
        
        # Initialize weights
        nn.init.xavier_uniform_(self.w_1.weight, gain=0.1)
        nn.init.xavier_uniform_(self.w_2.weight, gain=0.1)
    
    def forward(self, x):
        residual = x.clone()
        
        # First linear transformation
        x = self.w_1(x)
        x = safe_tensor_operation(x, "FFN w1")
        
        # ReLU activation
        x = F.relu(x)
        x = self.dropout(x)
        
        # Second linear transformation
        x = self.w_2(x)
        x = safe_tensor_operation(x, "FFN w2")
        x = self.dropout(x)
        
        # Residual connection
        x += residual
        
        # Layer normalization
        try:
            x = self.layer_norm(x)
            x = safe_tensor_operation(x, "FFN layer_norm")
        except Exception as e:
            print(f"⚠️  FFN LayerNorm failed: {e}, using manual normalization")
            mean = x.mean(dim=-1, keepdim=True)
            var = x.var(dim=-1, keepdim=True, unbiased=False)
            x = (x - mean) / torch.sqrt(var + 1e-6)
            x = safe_tensor_operation(x, "FFN manual norm")
        
        return x

def validate_model_inputs(inputs):
    """Validate SAITS model inputs."""
    print("🔍 Validating model inputs...")
    
    required_keys = ["X", "missing_mask"]
    for key in required_keys:
        if key not in inputs:
            raise ValueError(f"Missing required input: {key}")
        
        tensor = inputs[key]
        if not torch.is_tensor(tensor):
            raise ValueError(f"Input {key} must be a tensor")
        
        if tensor.numel() == 0:
            raise ValueError(f"Input {key} is empty")
        
        # Check for NaN/Inf
        if torch.isnan(tensor).any():
            print(f"⚠️  NaN detected in {key}, cleaning...")
            inputs[key] = torch.nan_to_num(tensor, nan=0.0)
        
        if torch.isinf(tensor).any():
            print(f"⚠️  Inf detected in {key}, cleaning...")
            inputs[key] = torch.nan_to_num(tensor, posinf=1e6, neginf=-1e6)
        
        # Check for extreme values
        min_val, max_val = tensor.min().item(), tensor.max().item()
        if abs(min_val) > 1e6 or abs(max_val) > 1e6:
            print(f"⚠️  Extreme values in {key}: [{min_val:.2e}, {max_val:.2e}]")
            inputs[key] = torch.clamp(tensor, -1e6, 1e6)
    
    print("✅ Input validation completed")
    return inputs

class StableEncoderLayer(nn.Module):
    """Stable encoder layer with self-attention and feed forward."""

    def __init__(self, d_time, d_feature, d_model, d_inner, n_head, d_k, d_v, dropout=0.1, **kwargs):
        super().__init__()
        self.slf_attn = StableMultiHeadAttention(d_model, n_head, d_k, d_v, dropout=dropout)
        self.pos_ffn = StablePositionwiseFeedForward(d_model, d_inner, dropout=dropout)

    def forward(self, enc_input, slf_attn_mask=None):
        enc_output, enc_slf_attn = self.slf_attn(
            enc_input, enc_input, enc_input, mask=slf_attn_mask)
        enc_output = self.pos_ffn(enc_output)
        return enc_output, enc_slf_attn

def stable_masked_mae_cal(inputs, target, mask):
    """Calculate masked mean absolute error with numerical stability."""
    # Clean inputs
    inputs = safe_tensor_operation(inputs, "MAE inputs")
    target = safe_tensor_operation(target, "MAE target")
    mask = safe_tensor_operation(mask, "MAE mask")

    # Ensure mask is in valid range
    mask = torch.clamp(mask, 0.0, 1.0)

    numerator = torch.sum(torch.abs(inputs - target) * mask)
    denominator = torch.sum(mask) + 1e-9

    result = numerator / denominator
    result = safe_tensor_operation(result, "MAE result")

    return result

class StableSAITS(nn.Module):
    """Numerically stable SAITS model for time series imputation."""

    def __init__(self, n_groups=1, n_group_inner_layers=1, d_time=100, d_feature=4,
                 d_model=64, d_inner=128, n_head=4, d_k=16, d_v=16, dropout=0.1,
                 input_with_mask=True, param_sharing_strategy="inner_group",
                 MIT=False, device='cpu', **kwargs):
        super().__init__()

        # Validate parameters
        self.n_groups = max(n_groups, 1)
        self.n_group_inner_layers = max(n_group_inner_layers, 1)
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT
        self.device = device

        # Ensure valid dimensions
        d_model = max(d_model, 4)
        d_inner = max(d_inner, d_model)
        n_head = max(n_head, 1)
        d_k = max(d_k, 1)
        d_v = max(d_v, 1)

        actual_d_feature = d_feature * 2 if self.input_with_mask else d_feature

        print(f"🔧 Creating StableSAITS: d_model={d_model}, n_head={n_head}, d_k={d_k}, d_v={d_v}")

        # Embedding layers
        self.embedding_1 = nn.Linear(actual_d_feature, d_model)
        self.embedding_2 = nn.Linear(actual_d_feature, d_model)

        # Positional encoding
        self.position_enc = StablePositionalEncoding(d_model, d_time)
        self.dropout = nn.Dropout(dropout)

        # Encoder layers for first block
        if self.param_sharing_strategy == "between_group":
            self.layer_stack_for_first_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_groups)
            ])
        else:
            self.layer_stack_for_first_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_group_inner_layers)
            ])

        # Encoder layers for second block
        if self.param_sharing_strategy == "between_group":
            self.layer_stack_for_second_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_groups)
            ])
        else:
            self.layer_stack_for_second_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_group_inner_layers)
            ])

        # Output layers
        self.reduce_dim_z = nn.Linear(d_model, d_feature)
        self.reduce_dim_beta = nn.Linear(d_model, d_feature)
        self.reduce_dim_gamma = nn.Linear(d_feature, d_feature)

        # Combination layer
        self.weight_combine = nn.Linear(d_feature + d_feature, d_feature)

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize model weights to prevent NaN."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def impute(self, inputs):
        # Validate inputs first
        inputs = validate_model_inputs(inputs)

        X, masks = inputs["X"], inputs["missing_mask"]

        debug_tensor(X, "SAITS X input", "SAITS")
        debug_tensor(masks, "SAITS masks input", "SAITS")

        # First DMSA block
        input_X_for_first = torch.cat([X, masks], dim=2) if self.input_with_mask else X
        input_X_for_first = safe_tensor_operation(input_X_for_first, "first block input")

        input_X_for_first = self.embedding_1(input_X_for_first)
        input_X_for_first = safe_tensor_operation(input_X_for_first, "first embedding")

        enc_output = self.dropout(self.position_enc(input_X_for_first))
        enc_output = safe_tensor_operation(enc_output, "first pos_enc")

        if self.param_sharing_strategy == "between_group":
            for _ in range(self.n_groups):
                for encoder_layer in self.layer_stack_for_first_block:
                    enc_output, _ = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "first block layer")
        else:
            for encoder_layer in self.layer_stack_for_first_block:
                for _ in range(self.n_group_inner_layers):
                    enc_output, _ = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "first block layer")

        X_tilde_1 = self.reduce_dim_z(enc_output)
        X_tilde_1 = safe_tensor_operation(X_tilde_1, "X_tilde_1")

        # Second DMSA block
        input_X_for_second = torch.cat([X, masks], dim=2) if self.input_with_mask else X
        input_X_for_second = safe_tensor_operation(input_X_for_second, "second block input")

        input_X_for_second = self.embedding_2(input_X_for_second)
        input_X_for_second = safe_tensor_operation(input_X_for_second, "second embedding")

        enc_output = self.dropout(self.position_enc(input_X_for_second))
        enc_output = safe_tensor_operation(enc_output, "second pos_enc")

        if self.param_sharing_strategy == "between_group":
            for _ in range(self.n_groups):
                for encoder_layer in self.layer_stack_for_second_block:
                    enc_output, attn_weights = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "second block layer")
        else:
            for encoder_layer in self.layer_stack_for_second_block:
                for _ in range(self.n_group_inner_layers):
                    enc_output, attn_weights = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "second block layer")

        X_tilde_2 = self.reduce_dim_gamma(F.relu(self.reduce_dim_beta(enc_output)))
        X_tilde_2 = safe_tensor_operation(X_tilde_2, "X_tilde_2")

        # Attention-weighted combination
        if attn_weights is not None:
            attn_weights = safe_tensor_operation(attn_weights, "attention weights")
            attn_weights = attn_weights.squeeze(dim=1) if attn_weights.dim() > 3 else attn_weights

            if len(attn_weights.shape) == 4:
                attn_weights = torch.transpose(attn_weights, 1, 3)
                attn_weights = attn_weights.mean(dim=3)
                attn_weights = torch.transpose(attn_weights, 1, 2)

            combining_weights = F.sigmoid(
                self.weight_combine(torch.cat([masks, attn_weights], dim=2))
            )
        else:
            # Fallback if attention weights are None
            combining_weights = F.sigmoid(self.weight_combine(torch.cat([masks, masks], dim=2)))

        combining_weights = safe_tensor_operation(combining_weights, "combining weights")

        # Combine X_tilde_1 and X_tilde_2
        X_tilde_3 = (1 - combining_weights) * X_tilde_2 + combining_weights * X_tilde_1
        X_tilde_3 = safe_tensor_operation(X_tilde_3, "X_tilde_3")

        # Replace non-missing part with original data
        X_c = masks * X + (1 - masks) * X_tilde_3
        X_c = safe_tensor_operation(X_c, "X_c final")

        debug_tensor(X_c, "SAITS output", "SAITS")

        return X_c, [X_tilde_1, X_tilde_2, X_tilde_3]

    def forward(self, inputs, stage="train"):
        try:
            X, masks = inputs["X"], inputs["missing_mask"]
            reconstruction_loss = 0

            imputed_data, [X_tilde_1, X_tilde_2, X_tilde_3] = self.impute(inputs)

            reconstruction_loss += stable_masked_mae_cal(X_tilde_1, X, masks)
            reconstruction_loss += stable_masked_mae_cal(X_tilde_2, X, masks)
            final_reconstruction_MAE = stable_masked_mae_cal(X_tilde_3, X, masks)
            reconstruction_loss += final_reconstruction_MAE
            reconstruction_loss /= 3

            if (self.MIT or stage == "val") and stage != "test":
                imputation_MAE = stable_masked_mae_cal(
                    X_tilde_3, inputs["X_holdout"], inputs["indicating_mask"]
                )
            else:
                imputation_MAE = torch.tensor(0.0, device=X.device)

            # Clean all outputs
            reconstruction_loss = safe_tensor_operation(reconstruction_loss, "reconstruction_loss")
            imputation_MAE = safe_tensor_operation(imputation_MAE, "imputation_MAE")
            final_reconstruction_MAE = safe_tensor_operation(final_reconstruction_MAE, "final_MAE")

            return {
                "imputed_data": imputed_data,
                "reconstruction_loss": reconstruction_loss,
                "imputation_loss": imputation_MAE,
                "reconstruction_MAE": final_reconstruction_MAE,
                "imputation_MAE": imputation_MAE,
            }

        except Exception as e:
            print(f"❌ SAITS forward pass failed: {e}")
            print(f"   Input shapes: X={inputs['X'].shape if 'X' in inputs else 'None'}")
            print(f"   Input shapes: mask={inputs['missing_mask'].shape if 'missing_mask' in inputs else 'None'}")
            raise e

class StableSAITSRegressor(BaseEstimator, RegressorMixin):
    """
    Stable SAITS Regressor wrapper with comprehensive NaN handling.

    This wrapper provides a robust sklearn-like interface for SAITS with:
    - Comprehensive input validation
    - Numerical stability improvements
    - Robust error handling
    - GPU optimization support
    """

    def __init__(self, sequence_length=50, n_groups=1, n_group_inner_layers=1,
                 d_model=64, d_inner=128, n_head=4, d_k=16, d_v=16, dropout=0.1,
                 epochs=100, batch_size=32, learning_rate=0.001, patience=10,
                 input_with_mask=True, param_sharing_strategy="inner_group",
                 MIT=False, random_state=42, device=None):

        # Store parameters
        self.sequence_length = max(sequence_length, 10)
        self.n_groups = max(n_groups, 1)
        self.n_group_inner_layers = max(n_group_inner_layers, 1)
        self.d_model = max(d_model, 4)
        self.d_inner = max(d_inner, self.d_model)
        self.n_head = max(n_head, 1)
        self.d_k = max(d_k, 1)
        self.d_v = max(d_v, 1)
        self.dropout = max(min(dropout, 0.9), 0.0)
        self.epochs = max(epochs, 1)
        self.batch_size = max(batch_size, 1)
        self.learning_rate = max(learning_rate, 1e-6)
        self.patience = max(patience, 1)
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT

        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)

        print(f"🔧 StableSAITSRegressor initialized with device: {self.device}")

        # Initialize components
        self.model = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.feature_names_ = None

        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)

    def _validate_and_clean_data(self, X, y=None, stage="fit"):
        """Comprehensive data validation and cleaning."""
        print(f"🔍 Validating {stage} data...")

        # Convert to numpy arrays
        X_array = np.array(X, dtype=np.float32)

        if X_array.size == 0:
            raise ValueError("Input data is empty")

        if len(X_array.shape) != 2:
            raise ValueError(f"Input data must be 2D, got shape {X_array.shape}")

        # Check for extreme values before NaN handling
        finite_mask = np.isfinite(X_array)
        if not np.any(finite_mask):
            raise ValueError("No finite values in input data")

        finite_data = X_array[finite_mask]
        data_min, data_max = np.min(finite_data), np.max(finite_data)
        data_std = np.std(finite_data)
        nan_ratio = 1 - (np.sum(finite_mask) / X_array.size)

        print(f"   📊 Data shape: {X_array.shape}")
        print(f"   📊 Data range: [{data_min:.6f}, {data_max:.6f}]")
        print(f"   📊 Data std: {data_std:.6f}")
        print(f"   📊 NaN ratio: {nan_ratio:.3f}")

        # Validate target if provided
        if y is not None:
            y_array = np.array(y, dtype=np.float32)
            if len(y_array) != len(X_array):
                raise ValueError(f"X and y length mismatch: {len(X_array)} vs {len(y_array)}")

            y_finite_mask = np.isfinite(y_array)
            if np.any(y_finite_mask):
                y_finite_data = y_array[y_finite_mask]
                y_min, y_max = np.min(y_finite_data), np.max(y_finite_data)
                y_std = np.std(y_finite_data)
                y_nan_ratio = 1 - (np.sum(y_finite_mask) / y_array.size)
                print(f"   📊 Target range: [{y_min:.6f}, {y_max:.6f}]")
                print(f"   📊 Target std: {y_std:.6f}")
                print(f"   📊 Target NaN ratio: {y_nan_ratio:.3f}")

        print("✅ Data validation completed")
        return X_array, y_array if y is not None else None

    def _prepare_data_stable(self, X, y=None, fit_scalers=False):
        """Stable data preparation with robust NaN handling."""
        print(f"🔧 Preparing data (fit_scalers={fit_scalers})...")

        X_array, y_array = self._validate_and_clean_data(X, y, "prepare")

        if fit_scalers:
            # Fit scalers using only finite values
            X_for_scaling = X_array.copy()

            # Handle NaN values column by column for more robust scaling
            for col in range(X_for_scaling.shape[1]):
                col_data = X_for_scaling[:, col]
                finite_mask = np.isfinite(col_data)

                if np.sum(finite_mask) > 0:
                    col_mean = np.mean(col_data[finite_mask])
                    X_for_scaling[~finite_mask, col] = col_mean
                else:
                    # If entire column is NaN, fill with zeros
                    X_for_scaling[:, col] = 0.0

            X_scaled = self.scaler_X.fit_transform(X_for_scaling)

            if y_array is not None:
                y_for_scaling = y_array.copy()
                finite_mask = np.isfinite(y_for_scaling)

                if np.sum(finite_mask) > 0:
                    y_mean = np.mean(y_for_scaling[finite_mask])
                    y_for_scaling[~finite_mask] = y_mean
                else:
                    y_for_scaling[:] = 0.0

                y_scaled = self.scaler_y.fit_transform(y_for_scaling.reshape(-1, 1)).flatten()
        else:
            # Transform using existing scalers
            X_for_scaling = X_array.copy()

            for col in range(X_for_scaling.shape[1]):
                col_data = X_for_scaling[:, col]
                finite_mask = np.isfinite(col_data)

                if np.sum(finite_mask) > 0:
                    col_mean = np.mean(col_data[finite_mask])
                    X_for_scaling[~finite_mask, col] = col_mean
                else:
                    X_for_scaling[:, col] = 0.0

            X_scaled = self.scaler_X.transform(X_for_scaling)

            if y_array is not None:
                y_for_scaling = y_array.copy()
                finite_mask = np.isfinite(y_for_scaling)

                if np.sum(finite_mask) > 0:
                    y_mean = np.mean(y_for_scaling[finite_mask])
                    y_for_scaling[~finite_mask] = y_mean
                else:
                    y_for_scaling[:] = 0.0

                y_scaled = self.scaler_y.transform(y_for_scaling.reshape(-1, 1)).flatten()

        # Final cleaning - replace any remaining NaN with zeros
        X_filled = np.nan_to_num(X_scaled, nan=0.0, posinf=1e6, neginf=-1e6)

        if y_array is not None:
            y_filled = np.nan_to_num(y_scaled, nan=0.0, posinf=1e6, neginf=-1e6)
            print(f"✅ Data prepared: X{X_filled.shape}, y{y_filled.shape}")
            return X_filled, y_filled
        else:
            print(f"✅ Data prepared: X{X_filled.shape}")
            return X_filled

    def _create_sequences_stable(self, X, y=None, original_X=None, original_y=None):
        """Create sequences with robust masking."""
        print(f"🔧 Creating sequences (length={self.sequence_length})...")

        sequences_X = []
        sequences_y = []
        sequences_mask = []

        for i in range(len(X) - self.sequence_length + 1):
            seq_X = X[i:i + self.sequence_length]
            sequences_X.append(seq_X)

            if y is not None:
                seq_y = y[i:i + self.sequence_length]
                sequences_y.append(seq_y)

            # Create mask based on original NaN values
            if original_X is not None:
                orig_seq_X = original_X[i:i + self.sequence_length]
                seq_mask_X = np.isfinite(orig_seq_X).astype(np.float32)
            else:
                seq_mask_X = np.ones_like(seq_X, dtype=np.float32)

            if original_y is not None:
                orig_seq_y = original_y[i:i + self.sequence_length]
                seq_mask_y = np.isfinite(orig_seq_y).astype(np.float32)
                seq_mask = np.concatenate([seq_mask_X, seq_mask_y.reshape(-1, 1)], axis=1)
            else:
                seq_mask = seq_mask_X

            sequences_mask.append(seq_mask)

        sequences_X = np.array(sequences_X, dtype=np.float32)
        sequences_mask = np.array(sequences_mask, dtype=np.float32)

        print(f"✅ Created {len(sequences_X)} sequences")

        if y is not None:
            sequences_y = np.array(sequences_y, dtype=np.float32)
            return sequences_X, sequences_y, sequences_mask
        else:
            return sequences_X, sequences_mask

    def fit(self, X, y, eval_set=None, verbose=False):
        """Fit the stable SAITS model."""
        print("🚀 Starting StableSAITS training...")

        self.feature_names_ = getattr(X, 'columns', None)

        # Store original data for mask creation
        X_orig = np.array(X, dtype=np.float32)
        y_orig = np.array(y, dtype=np.float32)

        # Prepare data with validation
        X_prep, y_prep = self._prepare_data_stable(X, y, fit_scalers=True)

        # Create sequences with proper masking
        X_seq, y_seq, combined_mask = self._create_sequences_stable(X_prep, y_prep, X_orig, y_orig)

        # Combine features and target for SAITS
        combined_data = np.concatenate([X_seq, y_seq.reshape(y_seq.shape[0], y_seq.shape[1], 1)], axis=2)

        # Final validation
        print(f"🔍 Final data validation:")
        print(f"   📊 Combined data shape: {combined_data.shape}")
        print(f"   📊 NaN count in combined_data: {np.sum(np.isnan(combined_data))}")
        print(f"   📊 NaN count in combined_mask: {np.sum(np.isnan(combined_mask))}")
        print(f"   📊 Data range: [{np.min(combined_data):.6f}, {np.max(combined_data):.6f}]")

        # Ensure absolutely no NaN values
        combined_data = np.nan_to_num(combined_data, nan=0.0, posinf=1e6, neginf=-1e6)
        combined_mask = np.nan_to_num(combined_mask, nan=0.0, posinf=1.0, neginf=0.0)

        # Adjust batch size if necessary
        max_sequences = len(combined_data)
        if self.batch_size > max_sequences:
            self.batch_size = max(1, max_sequences // 4)
            print(f"📊 Adjusted batch size to {self.batch_size} (max sequences: {max_sequences})")

        # Initialize model
        n_features = combined_data.shape[2]
        print(f"🔧 Initializing StableSAITS model with {n_features} features...")

        self.model = StableSAITS(
            n_groups=self.n_groups,
            n_group_inner_layers=self.n_group_inner_layers,
            d_time=self.sequence_length,
            d_feature=n_features,
            d_model=self.d_model,
            d_inner=self.d_inner,
            n_head=self.n_head,
            d_k=self.d_k,
            d_v=self.d_v,
            dropout=self.dropout,
            input_with_mask=self.input_with_mask,
            param_sharing_strategy=self.param_sharing_strategy,
            MIT=self.MIT,
            device=self.device
        ).to(self.device)

        # Setup optimizer
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)

        # Training loop with comprehensive error handling
        print(f"🎯 Starting training for {self.epochs} epochs...")
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.epochs):
            epoch_loss = 0
            n_batches = 0

            for i in range(0, len(combined_data), self.batch_size):
                batch_data = combined_data[i:i + self.batch_size]
                batch_mask = combined_mask[i:i + self.batch_size]

                # Final batch validation
                if np.any(np.isnan(batch_data)) or np.any(np.isnan(batch_mask)):
                    print(f"⚠️  NaN detected in batch {i//self.batch_size}, cleaning...")
                    batch_data = np.nan_to_num(batch_data, nan=0.0)
                    batch_mask = np.nan_to_num(batch_mask, nan=0.0)

                # Convert to tensors
                try:
                    X_tensor = torch.FloatTensor(batch_data).to(self.device)
                    mask_tensor = torch.FloatTensor(batch_mask).to(self.device)

                    # Final tensor validation
                    if torch.isnan(X_tensor).any() or torch.isinf(X_tensor).any():
                        print(f"⚠️  Cleaning X_tensor at epoch {epoch}")
                        X_tensor = torch.nan_to_num(X_tensor, nan=0.0, posinf=1e6, neginf=-1e6)

                    if torch.isnan(mask_tensor).any() or torch.isinf(mask_tensor).any():
                        print(f"⚠️  Cleaning mask_tensor at epoch {epoch}")
                        mask_tensor = torch.nan_to_num(mask_tensor, nan=0.0, posinf=1.0, neginf=0.0)

                    # Create target mask
                    target_mask = mask_tensor[:, :, -1:].clone()

                    # Prepare inputs for SAITS
                    inputs = {
                        "X": X_tensor,
                        "missing_mask": mask_tensor,
                        "X_holdout": X_tensor,
                        "indicating_mask": target_mask
                    }

                    optimizer.zero_grad()

                    # Forward pass with error handling
                    outputs = self.model(inputs, stage="train")
                    loss = outputs["reconstruction_loss"]

                    # Validate loss
                    if torch.isnan(loss) or torch.isinf(loss):
                        print(f"⚠️  Invalid loss at epoch {epoch}, batch {i//self.batch_size}: {loss}")
                        continue

                    loss.backward()

                    # Gradient clipping for stability
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                    optimizer.step()

                    epoch_loss += loss.item()
                    n_batches += 1

                except Exception as e:
                    print(f"❌ Batch {i//self.batch_size} failed at epoch {epoch}: {e}")
                    continue

            avg_loss = epoch_loss / n_batches if n_batches > 0 else float('inf')

            if verbose and epoch % 10 == 0:
                print(f"   Epoch {epoch:3d}: Loss = {avg_loss:.6f}")

            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= self.patience:
                    print(f"🛑 Early stopping at epoch {epoch}")
                    break

        print(f"✅ Training completed! Best loss: {best_loss:.6f}")
        return self

    def predict(self, X):
        """Predict using the trained stable SAITS model."""
        if self.model is None:
            raise ValueError("Model must be fitted before prediction")

        print("🔮 Starting StableSAITS prediction...")

        # Store original data for mask creation
        X_orig = np.array(X, dtype=np.float32)

        # Prepare data
        X_prep = self._prepare_data_stable(X, fit_scalers=False)

        # Create dummy target values for prediction
        dummy_target = np.zeros(len(X_prep), dtype=np.float32)
        dummy_target_orig = np.full(len(X_prep), np.nan, dtype=np.float32)

        # Create sequences with proper masking
        X_seq, mask_seq = self._create_sequences_stable(
            np.concatenate([X_prep, dummy_target.reshape(-1, 1)], axis=1),
            original_X=np.concatenate([X_orig, dummy_target_orig.reshape(-1, 1)], axis=1)
        )

        self.model.eval()
        predictions = []

        print(f"🔍 Predicting on {len(X_seq)} sequences...")

        with torch.no_grad():
            for i in range(0, len(X_seq), self.batch_size):
                batch_data = X_seq[i:i + self.batch_size]
                batch_mask = mask_seq[i:i + self.batch_size]

                try:
                    # Convert to tensors with validation
                    X_tensor = torch.FloatTensor(batch_data).to(self.device)
                    mask_tensor = torch.FloatTensor(batch_mask).to(self.device)

                    # Clean tensors
                    if torch.isnan(X_tensor).any() or torch.isinf(X_tensor).any():
                        X_tensor = torch.nan_to_num(X_tensor, nan=0.0, posinf=1e6, neginf=-1e6)

                    if torch.isnan(mask_tensor).any() or torch.isinf(mask_tensor).any():
                        mask_tensor = torch.nan_to_num(mask_tensor, nan=0.0, posinf=1.0, neginf=0.0)

                    # Prepare inputs for SAITS
                    inputs = {
                        "X": X_tensor,
                        "missing_mask": mask_tensor,
                        "X_holdout": X_tensor,
                        "indicating_mask": mask_tensor[:, :, -1:]
                    }

                    outputs = self.model(inputs, stage="test")
                    imputed_data = outputs["imputed_data"]

                    # Extract target predictions (last feature)
                    target_preds = imputed_data[:, :, -1].cpu().numpy()
                    predictions.extend(target_preds[:, -1])  # Take last timestep

                except Exception as e:
                    print(f"⚠️  Prediction batch {i//self.batch_size} failed: {e}")
                    # Fill with zeros for failed predictions
                    batch_size = len(batch_data)
                    predictions.extend([0.0] * batch_size)

        # Convert back to original scale
        predictions = np.array(predictions, dtype=np.float32).reshape(-1, 1)

        try:
            predictions_scaled = self.scaler_y.inverse_transform(predictions).flatten()
        except Exception as e:
            print(f"⚠️  Inverse transform failed: {e}, using raw predictions")
            predictions_scaled = predictions.flatten()

        # Handle the sequence length offset
        full_predictions = np.full(len(X), np.nan, dtype=np.float32)
        if len(predictions_scaled) > 0:
            start_idx = self.sequence_length - 1
            end_idx = start_idx + len(predictions_scaled)
            if end_idx <= len(full_predictions):
                full_predictions[start_idx:end_idx] = predictions_scaled

        print(f"✅ Prediction completed! Generated {np.sum(~np.isnan(full_predictions))} predictions")
        return full_predictions

# Test function
def test_stable_saits():
    """Test the stable SAITS implementation."""
    print("🧪 Testing StableSAITS implementation...")

    # Create test data
    np.random.seed(42)
    n_samples = 200
    n_features = 4

    X = np.random.randn(n_samples, n_features)
    y = np.random.randn(n_samples)

    # Add some NaN values
    X[np.random.choice(n_samples, 20), np.random.choice(n_features, 1)] = np.nan
    y[np.random.choice(n_samples, 10)] = np.nan

    print(f"   📊 Test data: X{X.shape}, y{y.shape}")
    print(f"   📊 NaN ratio X: {np.sum(np.isnan(X)) / X.size:.3f}")
    print(f"   📊 NaN ratio y: {np.sum(np.isnan(y)) / y.size:.3f}")

    # Test the model
    try:
        model = StableSAITSRegressor(
            sequence_length=20,
            epochs=5,
            batch_size=16
        )

        model.fit(X, y, verbose=True)
        predictions = model.predict(X)

        print(f"   ✅ Predictions shape: {predictions.shape}")
        print(f"   ✅ Valid predictions: {np.sum(~np.isnan(predictions))}")
        print("✅ StableSAITS test passed!")

    except Exception as e:
        print(f"❌ StableSAITS test failed: {e}")
        return False

    return True

if __name__ == "__main__":
    print("🚀 StableSAITS Model - Testing")
    print("=" * 50)
    test_stable_saits()
