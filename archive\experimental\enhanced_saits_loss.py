#!/usr/bin/env python3
"""
Enhanced SAITS Loss Function inspired by PyPOTS implementation.

This module implements the sophisticated loss calculation used in the original
PyPOTS SAITS implementation, featuring separate ORT and MIT loss components.
"""

import torch
import torch.nn as nn
import numpy as np


class EnhancedSAITSLoss(nn.Module):
    """
    Enhanced SAITS loss function with ORT and MIT components.
    
    Based on the PyPOTS implementation, this loss function properly handles:
    - ORT (Observed Reconstruction Task): Reconstruction of observed values
    - MIT (Masked Imputation Task): Imputation of artificially missing values
    
    Args:
        ORT_weight (float): Weight for the ORT loss component
        MIT_weight (float): Weight for the MIT loss component
        reduction (str): Reduction method ('mean', 'sum', 'none')
    """
    
    def __init__(self, ORT_weight=1.0, MIT_weight=1.0, reduction='mean'):
        super().__init__()
        self.ORT_weight = ORT_weight
        self.MIT_weight = MIT_weight
        self.reduction = reduction
        
    def masked_mse_loss(self, predictions, targets, mask):
        """
        Calculate masked MSE loss.
        
        Args:
            predictions (torch.Tensor): Predicted values
            targets (torch.Tensor): Target values
            mask (torch.Tensor): Binary mask (1 for valid, 0 for invalid)
            
        Returns:
            torch.Tensor: Masked MSE loss
        """
        # Ensure mask is float for proper computation
        mask = mask.float()
        
        # Calculate squared differences
        diff = (predictions - targets) ** 2
        
        # Apply mask
        masked_diff = diff * mask
        
        # Calculate loss based on reduction method
        if self.reduction == 'mean':
            # Average over valid elements only
            valid_elements = mask.sum()
            if valid_elements > 0:
                return masked_diff.sum() / valid_elements
            else:
                return torch.tensor(0.0, device=predictions.device)
        elif self.reduction == 'sum':
            return masked_diff.sum()
        else:  # 'none'
            return masked_diff
    
    def forward(self, model_outputs, targets, masks):
        """
        Calculate the combined SAITS loss.
        
        Args:
            model_outputs (dict): Dictionary containing:
                - 'X_tilde_1': First representation output
                - 'X_tilde_2': Second representation output  
                - 'X_tilde_3': Final representation output
            targets (dict): Dictionary containing:
                - 'X': Input data with missing values
                - 'X_ori': Original data without artificial missing values
            masks (dict): Dictionary containing:
                - 'missing_mask': Mask for observed values (1=observed, 0=missing)
                - 'indicating_mask': Mask for artificially missing values
                
        Returns:
            dict: Dictionary containing:
                - 'loss': Total loss
                - 'ORT_loss': Observed reconstruction task loss
                - 'MIT_loss': Masked imputation task loss
        """
        # Extract model outputs
        X_tilde_1 = model_outputs['X_tilde_1']
        X_tilde_2 = model_outputs['X_tilde_2'] 
        X_tilde_3 = model_outputs['X_tilde_3']
        
        # Extract targets
        X_input = targets['X']
        X_ori = targets['X_ori']
        
        # Extract masks
        missing_mask = masks['missing_mask']
        indicating_mask = masks['indicating_mask']
        
        # Calculate ORT Loss (Observed Reconstruction Task)
        # This measures how well the model reconstructs observed values
        ORT_loss = 0.0
        ORT_loss += self.masked_mse_loss(X_tilde_1, X_input, missing_mask)
        ORT_loss += self.masked_mse_loss(X_tilde_2, X_input, missing_mask)
        ORT_loss += self.masked_mse_loss(X_tilde_3, X_input, missing_mask)
        ORT_loss /= 3.0  # Average across three representations
        ORT_loss *= self.ORT_weight
        
        # Calculate MIT Loss (Masked Imputation Task)
        # This measures how well the model imputes artificially missing values
        MIT_loss = self.masked_mse_loss(X_tilde_3, X_ori, indicating_mask)
        MIT_loss *= self.MIT_weight
        
        # Total loss
        total_loss = ORT_loss + MIT_loss
        
        return {
            'loss': total_loss,
            'ORT_loss': ORT_loss,
            'MIT_loss': MIT_loss
        }


class MaskGenerator:
    """
    Utility class for generating masks for SAITS training.
    
    This class implements the mask generation strategy used in PyPOTS,
    which creates artificial missing values for training the MIT component.
    """
    
    @staticmethod
    def create_artificial_missing(X, missing_rate=0.1, missing_pattern='MCAR'):
        """
        Create artificial missing values in the data.
        
        Args:
            X (np.ndarray): Original data
            missing_rate (float): Proportion of values to make missing
            missing_pattern (str): Missing pattern ('MCAR' for Missing Completely At Random)
            
        Returns:
            tuple: (X_with_missing, artificial_missing_mask)
        """
        if missing_pattern == 'MCAR':
            # Missing Completely At Random
            artificial_missing_mask = np.random.random(X.shape) < missing_rate
        else:
            raise ValueError(f"Unsupported missing pattern: {missing_pattern}")
        
        # Create data with artificial missing values
        X_with_missing = X.copy()
        X_with_missing[artificial_missing_mask] = np.nan
        
        return X_with_missing, artificial_missing_mask
    
    @staticmethod
    def generate_masks(X, X_ori):
        """
        Generate all necessary masks for SAITS training.
        
        Args:
            X (np.ndarray): Input data with missing values
            X_ori (np.ndarray): Original data without artificial missing values
            
        Returns:
            dict: Dictionary containing all masks
        """
        # Missing mask: 1 for observed values, 0 for missing values
        missing_mask = ~np.isnan(X)
        
        # Original missing mask: 1 for originally observed values
        ori_missing_mask = ~np.isnan(X_ori)
        
        # Indicating mask: 1 for artificially missing values
        # These are values that were originally observed but made missing for training
        indicating_mask = ori_missing_mask & ~missing_mask
        
        return {
            'missing_mask': missing_mask.astype(np.float32),
            'indicating_mask': indicating_mask.astype(np.float32),
            'ori_missing_mask': ori_missing_mask.astype(np.float32)
        }
    
    @staticmethod
    def prepare_training_data(X, missing_rate=0.1):
        """
        Prepare data for SAITS training with proper masks.
        
        Args:
            X (np.ndarray): Original complete data
            missing_rate (float): Rate of artificial missing values
            
        Returns:
            dict: Dictionary containing prepared data and masks
        """
        # Store original data
        X_ori = X.copy()
        
        # Create artificial missing values
        X_with_missing, _ = MaskGenerator.create_artificial_missing(X, missing_rate)
        
        # Generate all masks
        masks = MaskGenerator.generate_masks(X_with_missing, X_ori)
        
        return {
            'X': X_with_missing,
            'X_ori': X_ori,
            'masks': masks
        }


def test_enhanced_loss():
    """Test the enhanced SAITS loss function."""
    print("🧪 Testing Enhanced SAITS Loss Function")
    
    # Create sample data
    batch_size, seq_len, n_features = 4, 10, 3
    
    # Generate sample model outputs
    model_outputs = {
        'X_tilde_1': torch.randn(batch_size, seq_len, n_features),
        'X_tilde_2': torch.randn(batch_size, seq_len, n_features),
        'X_tilde_3': torch.randn(batch_size, seq_len, n_features)
    }
    
    # Generate sample targets and masks
    X_ori = torch.randn(batch_size, seq_len, n_features)
    X_input = X_ori.clone()
    
    # Create some missing values
    missing_mask = torch.ones_like(X_input)
    missing_mask[0, :3, :] = 0  # Some missing values
    
    indicating_mask = torch.zeros_like(X_input)
    indicating_mask[0, 5:8, :] = 1  # Some artificially missing values
    
    targets = {'X': X_input, 'X_ori': X_ori}
    masks = {'missing_mask': missing_mask, 'indicating_mask': indicating_mask}
    
    # Test loss calculation
    loss_fn = EnhancedSAITSLoss(ORT_weight=1.0, MIT_weight=1.0)
    loss_dict = loss_fn(model_outputs, targets, masks)
    
    print(f"✅ Total Loss: {loss_dict['loss']:.4f}")
    print(f"✅ ORT Loss: {loss_dict['ORT_loss']:.4f}")
    print(f"✅ MIT Loss: {loss_dict['MIT_loss']:.4f}")
    
    # Test mask generator
    print("\n🔧 Testing Mask Generator")
    X_sample = np.random.randn(20, 5)
    prepared_data = MaskGenerator.prepare_training_data(X_sample, missing_rate=0.2)
    
    print(f"✅ Original shape: {X_sample.shape}")
    print(f"✅ Missing values created: {np.isnan(prepared_data['X']).sum()}")
    print(f"✅ Indicating mask sum: {prepared_data['masks']['indicating_mask'].sum()}")
    
    print("\n🎉 Enhanced SAITS Loss Function test completed!")


if __name__ == "__main__":
    test_enhanced_loss()
