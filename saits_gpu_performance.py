"""
SAITS GPU Performance Optimization Module
=========================================

This module provides GPU acceleration and performance optimization for SAITS models.

Key Features:
1. Automatic GPU detection and configuration
2. Memory optimization for large datasets
3. Mixed precision training support
4. Batch processing optimization
5. Performance monitoring and profiling
"""

import torch
import torch.nn as nn
from torch.cuda.amp import autocast, GradScaler
import numpy as np
import logging
import time
from typing import Dict, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SAITSGPUOptimizer:
    """
    GPU optimization manager for SAITS models.
    """

    def __init__(self, enable_mixed_precision: bool = True,
                 memory_fraction: float = 0.8,
                 benchmark_mode: bool = True):
        """
        Initialize GPU optimizer.

        Args:
            enable_mixed_precision: Whether to use mixed precision training
            memory_fraction: Fraction of GPU memory to use
            benchmark_mode: Enable cudnn benchmark for consistent input sizes
        """
        self.enable_mixed_precision = enable_mixed_precision
        self.memory_fraction = memory_fraction
        self.benchmark_mode = benchmark_mode

        # Initialize GPU settings
        self.device = self._setup_gpu()
        self.scaler = GradScaler() if enable_mixed_precision and self.device.type == 'cuda' else None

        # Performance tracking
        self.performance_stats = {
            'training_times': [],
            'memory_usage': [],
            'batch_processing_times': []
        }

        logger.info(f"SAITSGPUOptimizer initialized with device: {self.device}")

    def _setup_gpu(self) -> torch.device:
        """
        Setup GPU configuration.

        Returns:
            Configured device
        """
        if not torch.cuda.is_available():
            logger.warning("CUDA not available, using CPU")
            return torch.device('cpu')

        # Set memory fraction
        if hasattr(torch.cuda, 'set_memory_fraction'):
            torch.cuda.set_memory_fraction(self.memory_fraction)

        # Enable benchmark mode for consistent input sizes
        if self.benchmark_mode:
            torch.backends.cudnn.benchmark = True
            logger.info("Enabled cudnn benchmark mode")

        # Get GPU info
        device = torch.device('cuda')
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3

        logger.info(f"Using GPU: {gpu_name} ({gpu_memory:.1f} GB)")

        return device

    def optimize_batch_size(self, model: nn.Module, input_shape: Tuple[int, ...],
                           max_batch_size: int = 128) -> int:
        """
        Find optimal batch size for the given model and input shape.

        Args:
            model: SAITS model
            input_shape: Shape of input data (sequence_length, n_features)
            max_batch_size: Maximum batch size to test

        Returns:
            Optimal batch size
        """
        if self.device.type == 'cpu':
            return min(32, max_batch_size)  # Conservative for CPU

        model.eval()
        optimal_batch_size = 1

        # Test different batch sizes
        for batch_size in [1, 2, 4, 8, 16, 32, 64, 128]:
            if batch_size > max_batch_size:
                break

            try:
                # Create dummy input
                dummy_input = {
                    'X': torch.randn(batch_size, *input_shape).to(self.device),
                    'missing_mask': torch.ones(batch_size, *input_shape).to(self.device),
                    'indicating_mask': torch.ones(batch_size, *input_shape).to(self.device)
                }

                # Test forward pass
                with torch.no_grad():
                    _ = model(dummy_input)

                optimal_batch_size = batch_size
                logger.debug(f"Batch size {batch_size} works")

            except RuntimeError as e:
                if "out of memory" in str(e):
                    logger.debug(f"Batch size {batch_size} causes OOM")
                    break
                else:
                    raise e
            finally:
                # Clear cache
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()

        logger.info(f"Optimal batch size: {optimal_batch_size}")
        return optimal_batch_size

    def get_memory_usage(self) -> Dict[str, float]:
        """
        Get current GPU memory usage.

        Returns:
            Dictionary with memory statistics
        """
        if self.device.type == 'cpu':
            return {'allocated': 0, 'cached': 0, 'total': 0}

        allocated = torch.cuda.memory_allocated() / 1024**3
        cached = torch.cuda.memory_reserved() / 1024**3
        total = torch.cuda.get_device_properties(0).total_memory / 1024**3

        return {
            'allocated': allocated,
            'cached': cached,
            'total': total,
            'utilization': allocated / total * 100
        }

    def optimize_model_for_gpu(self, model: nn.Module) -> nn.Module:
        """
        Optimize model for GPU execution.

        Args:
            model: SAITS model to optimize

        Returns:
            Optimized model
        """
        # Move model to device
        model = model.to(self.device)

        # Enable mixed precision if available
        if self.enable_mixed_precision and self.device.type == 'cuda':
            logger.info("Model optimized for mixed precision training")

        # Compile model if PyTorch 2.0+ is available
        if hasattr(torch, 'compile'):
            try:
                model = torch.compile(model)
                logger.info("Model compiled with torch.compile")
            except Exception as e:
                logger.warning(f"Could not compile model: {e}")

        return model