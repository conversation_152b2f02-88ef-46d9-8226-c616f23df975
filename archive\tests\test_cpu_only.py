#!/usr/bin/env python3
"""
Test CPU-only SAITS to isolate device issues.
"""

import time
import numpy as np
import torch
from sklearn.model_selection import train_test_split
from sklearn.datasets import make_regression
from saits_model import SAITSRegressor

def test_cpu_only():
    """Test SAITS with CPU only."""
    print("🧪 Testing CPU-only SAITS...")
    
    # Create test data
    X, y = make_regression(n_samples=200, n_features=4, noise=0.1, random_state=42)
    
    # Add missing values
    mask = np.random.random(X.shape) < 0.2
    X[mask] = np.nan
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    print(f"Data: {X_train.shape[0]} train, {X_test.shape[0]} test")
    
    # Create CPU-only model
    model = SAITSRegressor(
        sequence_length=20,
        batch_size=16,
        d_model=32,
        d_inner=64,
        epochs=5,
        patience=3
    )
    
    # Force CPU
    model.device = torch.device('cpu')
    
    try:
        print("🏋️  Training...")
        start_time = time.time()
        
        # Fit without GPU optimization
        model.fit(X_train, y_train, verbose=True)
        
        training_time = time.time() - start_time
        print(f"✅ Training completed in {training_time:.1f}s")
        
        print("🔮 Predicting...")
        start_time = time.time()
        
        predictions = model.predict(X_test)

        prediction_time = time.time() - start_time
        print(f"✅ Prediction completed in {prediction_time:.1f}s")

        # Check predictions for NaN
        print(f"Predictions shape: {predictions.shape}")
        print(f"NaN count in predictions: {np.isnan(predictions).sum()}")
        print(f"Predictions range: [{np.nanmin(predictions):.4f}, {np.nanmax(predictions):.4f}]")

        # Filter out NaN predictions for metrics
        valid_mask = ~np.isnan(predictions)
        if valid_mask.sum() == 0:
            print("❌ All predictions are NaN!")
            return False

        valid_predictions = predictions[valid_mask]
        valid_y_test = y_test[valid_mask]

        # Calculate metrics
        from sklearn.metrics import mean_absolute_error, r2_score
        mae = mean_absolute_error(valid_y_test, valid_predictions)
        r2 = r2_score(valid_y_test, valid_predictions)
        
        print(f"📊 Results: MAE={mae:.4f}, R²={r2:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cpu_only()
    if success:
        print("✅ CPU-only test passed!")
    else:
        print("❌ CPU-only test failed!")
