"""
SAITS (Self-Attention-based Imputation for Time Series) model implementation
for well log prediction and imputation.

This module adapts the SAITS model to work with the existing ML log prediction framework.
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, RegressorMixin
import warnings
warnings.filterwarnings('ignore')


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer models."""
    
    def __init__(self, d_model, n_position=200):
        super().__init__()
        self.d_model = d_model
        
        # Create positional encoding matrix
        pe = torch.zeros(n_position, d_model)
        position = torch.arange(0, n_position, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        # Keep pe as (n_position, d_model) - don't transpose

        self.register_buffer('pe', pe)

    def forward(self, x):
        # x shape: (batch_size, sequence_length, d_model)
        # self.pe shape: (n_position, d_model)
        # We need to slice by sequence_length and broadcast to batch dimension
        seq_len = x.size(1)
        return x + self.pe[:seq_len, :].unsqueeze(0)


class MultiHeadAttention(nn.Module):
    """Multi-head attention mechanism."""
    
    def __init__(self, d_model, n_head, d_k, d_v, dropout=0.1):
        super().__init__()
        self.n_head = n_head
        self.d_k = d_k
        self.d_v = d_v
        
        self.w_qs = nn.Linear(d_model, n_head * d_k, bias=False)
        self.w_ks = nn.Linear(d_model, n_head * d_k, bias=False)
        self.w_vs = nn.Linear(d_model, n_head * d_v, bias=False)
        self.fc = nn.Linear(n_head * d_v, d_model, bias=False)
        
        self.attention = ScaledDotProductAttention(temperature=d_k ** 0.5)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)
    
    def forward(self, q, k, v, mask=None):
        d_k, d_v, n_head = self.d_k, self.d_v, self.n_head
        sz_b, len_q, len_k, len_v = q.size(0), q.size(1), k.size(1), v.size(1)
        
        residual = q
        
        # Pass through the pre-attention projection: b x lq x (n*dv)
        q = self.w_qs(q).view(sz_b, len_q, n_head, d_k)
        k = self.w_ks(k).view(sz_b, len_k, n_head, d_k)
        v = self.w_vs(v).view(sz_b, len_v, n_head, d_v)
        
        # Transpose for attention dot product: b x n x lq x dv
        q, k, v = q.transpose(1, 2), k.transpose(1, 2), v.transpose(1, 2)
        
        if mask is not None:
            mask = mask.unsqueeze(1)   # For head axis broadcasting.
        
        q, attn = self.attention(q, k, v, mask=mask)
        
        # Transpose to move the head dimension back: b x lq x n x dv
        q = q.transpose(1, 2).contiguous().view(sz_b, len_q, -1)
        
        q = self.dropout(self.fc(q))
        q += residual
        
        q = self.layer_norm(q)
        
        return q, attn


class ScaledDotProductAttention(nn.Module):
    """Scaled Dot-Product Attention."""
    
    def __init__(self, temperature, attn_dropout=0.1):
        super().__init__()
        self.temperature = temperature
        self.dropout = nn.Dropout(attn_dropout)
    
    def forward(self, q, k, v, mask=None):
        # Add safeguards against NaN
        q = torch.nan_to_num(q, nan=0.0, posinf=1e6, neginf=-1e6)
        k = torch.nan_to_num(k, nan=0.0, posinf=1e6, neginf=-1e6)
        v = torch.nan_to_num(v, nan=0.0, posinf=1e6, neginf=-1e6)

        attn = torch.matmul(q / self.temperature, k.transpose(2, 3))
        attn = torch.nan_to_num(attn, nan=0.0, posinf=1e6, neginf=-1e6)

        if mask is not None:
            mask = torch.nan_to_num(mask, nan=0.0, posinf=1.0, neginf=0.0)
            attn = attn.masked_fill(mask == 0, -1e9)

        attn = F.softmax(attn, dim=-1)
        attn = torch.nan_to_num(attn, nan=0.0, posinf=1.0, neginf=0.0)
        attn = self.dropout(attn)

        output = torch.matmul(attn, v)
        output = torch.nan_to_num(output, nan=0.0, posinf=1e6, neginf=-1e6)

        return output, attn


class PositionwiseFeedForward(nn.Module):
    """Position-wise feed forward network."""
    
    def __init__(self, d_in, d_hid, dropout=0.1):
        super().__init__()
        self.w_1 = nn.Linear(d_in, d_hid)
        self.w_2 = nn.Linear(d_hid, d_in)
        self.layer_norm = nn.LayerNorm(d_in, eps=1e-6)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        residual = x
        
        x = self.w_2(F.relu(self.w_1(x)))
        x = self.dropout(x)
        x += residual
        
        x = self.layer_norm(x)
        
        return x


class EncoderLayer(nn.Module):
    """Encoder layer with self-attention and feed forward."""
    
    def __init__(self, d_time, d_feature, d_model, d_inner, n_head, d_k, d_v, dropout=0.1, **kwargs):
        super().__init__()
        self.slf_attn = MultiHeadAttention(d_model, n_head, d_k, d_v, dropout=dropout)
        self.pos_ffn = PositionwiseFeedForward(d_model, d_inner, dropout=dropout)
    
    def forward(self, enc_input, slf_attn_mask=None):
        enc_output, enc_slf_attn = self.slf_attn(
            enc_input, enc_input, enc_input, mask=slf_attn_mask)
        enc_output = self.pos_ffn(enc_output)
        return enc_output, enc_slf_attn


def masked_mae_cal(inputs, target, mask):
    """Calculate masked mean absolute error."""
    # Add safeguards against NaN
    inputs = torch.nan_to_num(inputs, nan=0.0, posinf=1e6, neginf=-1e6)
    target = torch.nan_to_num(target, nan=0.0, posinf=1e6, neginf=-1e6)
    mask = torch.nan_to_num(mask, nan=0.0, posinf=1.0, neginf=0.0)

    numerator = torch.sum(torch.abs(inputs - target) * mask)
    denominator = torch.sum(mask) + 1e-9

    result = numerator / denominator
    return torch.nan_to_num(result, nan=0.0, posinf=1e6, neginf=-1e6)


class SAITS(nn.Module):
    """SAITS model for time series imputation."""
    
    def __init__(self, n_groups=1, n_group_inner_layers=1, d_time=100, d_feature=4, 
                 d_model=64, d_inner=128, n_head=4, d_k=16, d_v=16, dropout=0.1, 
                 input_with_mask=True, param_sharing_strategy="inner_group", 
                 MIT=False, device='cpu', **kwargs):
        super().__init__()
        self.n_groups = n_groups
        self.n_group_inner_layers = n_group_inner_layers
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT
        self.device = device
        
        actual_d_feature = d_feature * 2 if self.input_with_mask else d_feature
        
        if param_sharing_strategy == "between_group":
            # For between_group, only need to create 1 group and repeat n_groups times
            self.layer_stack_for_first_block = nn.ModuleList([
                EncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout, **kwargs)
                for _ in range(n_group_inner_layers)
            ])
            self.layer_stack_for_second_block = nn.ModuleList([
                EncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout, **kwargs)
                for _ in range(n_group_inner_layers)
            ])
        else:
            # For inner_group, create n_groups layers and repeat n_group_inner_layers times
            self.layer_stack_for_first_block = nn.ModuleList([
                EncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout, **kwargs)
                for _ in range(n_groups)
            ])
            self.layer_stack_for_second_block = nn.ModuleList([
                EncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout, **kwargs)
                for _ in range(n_groups)
            ])
        
        self.dropout = nn.Dropout(p=dropout)
        self.position_enc = PositionalEncoding(d_model, n_position=d_time)
        
        # For the 1st block
        self.embedding_1 = nn.Linear(actual_d_feature, d_model)
        self.reduce_dim_z = nn.Linear(d_model, d_feature)
        
        # For the 2nd block
        self.embedding_2 = nn.Linear(actual_d_feature, d_model)
        self.reduce_dim_beta = nn.Linear(d_model, d_feature)
        self.reduce_dim_gamma = nn.Linear(d_feature, d_feature)
        
        # For the 3rd block
        self.weight_combine = nn.Linear(d_feature + d_time, d_feature)
    
    def impute(self, inputs):
        X, masks = inputs["X"], inputs["missing_mask"]
        
        # First DMSA block
        input_X_for_first = torch.cat([X, masks], dim=2) if self.input_with_mask else X
        input_X_for_first = self.embedding_1(input_X_for_first)
        enc_output = self.dropout(self.position_enc(input_X_for_first))
        
        if self.param_sharing_strategy == "between_group":
            for _ in range(self.n_groups):
                for encoder_layer in self.layer_stack_for_first_block:
                    enc_output, _ = encoder_layer(enc_output)
        else:
            for encoder_layer in self.layer_stack_for_first_block:
                for _ in range(self.n_group_inner_layers):
                    enc_output, _ = encoder_layer(enc_output)
        
        X_tilde_1 = self.reduce_dim_z(enc_output)
        X_prime = masks * X + (1 - masks) * X_tilde_1
        
        # Second DMSA block
        input_X_for_second = torch.cat([X_prime, masks], dim=2) if self.input_with_mask else X_prime
        input_X_for_second = self.embedding_2(input_X_for_second)
        enc_output = self.position_enc(input_X_for_second)
        
        if self.param_sharing_strategy == "between_group":
            for _ in range(self.n_groups):
                for encoder_layer in self.layer_stack_for_second_block:
                    enc_output, attn_weights = encoder_layer(enc_output)
        else:
            for encoder_layer in self.layer_stack_for_second_block:
                for _ in range(self.n_group_inner_layers):
                    enc_output, attn_weights = encoder_layer(enc_output)
        
        X_tilde_2 = self.reduce_dim_gamma(F.relu(self.reduce_dim_beta(enc_output)))
        
        # Attention-weighted combination block
        attn_weights = attn_weights.squeeze(dim=1)
        if len(attn_weights.shape) == 4:
            # Average attention weights from all heads
            attn_weights = torch.transpose(attn_weights, 1, 3)
            attn_weights = attn_weights.mean(dim=3)
            attn_weights = torch.transpose(attn_weights, 1, 2)
        
        combining_weights = F.sigmoid(
            self.weight_combine(torch.cat([masks, attn_weights], dim=2))
        )
        
        # Combine X_tilde_1 and X_tilde_2
        X_tilde_3 = (1 - combining_weights) * X_tilde_2 + combining_weights * X_tilde_1
        
        # Replace non-missing part with original data
        X_c = masks * X + (1 - masks) * X_tilde_3
        
        return X_c, [X_tilde_1, X_tilde_2, X_tilde_3]
    
    def forward(self, inputs, stage="train"):
        X, masks = inputs["X"], inputs["missing_mask"]
        reconstruction_loss = 0
        
        imputed_data, [X_tilde_1, X_tilde_2, X_tilde_3] = self.impute(inputs)
        
        reconstruction_loss += masked_mae_cal(X_tilde_1, X, masks)
        reconstruction_loss += masked_mae_cal(X_tilde_2, X, masks)
        final_reconstruction_MAE = masked_mae_cal(X_tilde_3, X, masks)
        reconstruction_loss += final_reconstruction_MAE
        reconstruction_loss /= 3
        
        if (self.MIT or stage == "val") and stage != "test":
            imputation_MAE = masked_mae_cal(
                X_tilde_3, inputs["X_holdout"], inputs["indicating_mask"]
            )
        else:
            # Create tensor on the same device as the model
            imputation_MAE = torch.tensor(0.0, device=X.device)
        
        return {
            "imputed_data": imputed_data,
            "reconstruction_loss": reconstruction_loss,
            "imputation_loss": imputation_MAE,
            "reconstruction_MAE": final_reconstruction_MAE,
            "imputation_MAE": imputation_MAE,
        }


class SAITSRegressor(BaseEstimator, RegressorMixin):
    """
    SAITS Regressor wrapper that adapts SAITS for sklearn-like interface.

    This wrapper transforms tabular well log data into time series format
    and uses SAITS for imputation and prediction.
    """

    def __init__(self, sequence_length=50, n_groups=1, n_group_inner_layers=1,
                 d_model=64, d_inner=128, n_head=4, d_k=16, d_v=16, dropout=0.1,
                 epochs=100, batch_size=32, learning_rate=0.001, patience=10,
                 input_with_mask=True, param_sharing_strategy="inner_group",
                 MIT=False, random_state=42, **kwargs):

        self.sequence_length = sequence_length
        self.n_groups = n_groups
        self.n_group_inner_layers = n_group_inner_layers
        self.d_model = d_model
        self.d_inner = d_inner
        self.n_head = n_head
        self.d_k = d_k
        self.d_v = d_v
        self.dropout = dropout
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.patience = patience
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT
        self.random_state = random_state

        # Set device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Initialize components
        self.model = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.feature_names_ = None
        self.target_name_ = None

        # Set random seeds
        torch.manual_seed(random_state)
        np.random.seed(random_state)

    def _create_sequences(self, X, y=None, original_X=None, original_y=None):
        """Convert tabular data to sequences for SAITS."""
        sequences_X = []
        sequences_y = []
        sequences_mask = []

        n_samples = len(X)

        for i in range(n_samples - self.sequence_length + 1):
            seq_X = X[i:i + self.sequence_length]
            sequences_X.append(seq_X)

            if y is not None:
                seq_y = y[i:i + self.sequence_length]
                sequences_y.append(seq_y)

            # Create mask based on original NaN values (before preprocessing)
            if original_X is not None:
                orig_seq_X = original_X[i:i + self.sequence_length]
                seq_mask_X = ~np.isnan(orig_seq_X)
            else:
                seq_mask_X = np.ones_like(seq_X, dtype=bool)

            if original_y is not None:
                orig_seq_y = original_y[i:i + self.sequence_length]
                seq_mask_y = ~np.isnan(orig_seq_y)
                # Combine X and y masks
                seq_mask = np.concatenate([seq_mask_X, seq_mask_y.reshape(-1, 1)], axis=1)
            else:
                seq_mask = seq_mask_X

            sequences_mask.append(seq_mask)

        sequences_X = np.array(sequences_X)
        sequences_mask = np.array(sequences_mask, dtype=np.float32)

        if y is not None:
            sequences_y = np.array(sequences_y)
            return sequences_X, sequences_y, sequences_mask
        else:
            return sequences_X, sequences_mask

    def _prepare_data(self, X, y=None, fit_scalers=False):
        """Prepare data for SAITS training/prediction."""
        X_array = np.array(X)

        if fit_scalers:
            # For fitting, we need to handle NaN values in the scaler
            # Use only non-NaN values for fitting the scaler
            X_for_scaling = X_array.copy()
            for col in range(X_for_scaling.shape[1]):
                col_data = X_for_scaling[:, col]
                if np.any(np.isnan(col_data)):
                    # Fill NaN with column mean for scaling
                    col_mean = np.nanmean(col_data)
                    X_for_scaling[:, col] = np.where(np.isnan(col_data), col_mean, col_data)

            X_scaled = self.scaler_X.fit_transform(X_for_scaling)

            if y is not None:
                y_array = np.array(y).reshape(-1, 1)
                # Handle NaN in target
                y_for_scaling = y_array.copy()
                if np.any(np.isnan(y_for_scaling)):
                    y_mean = np.nanmean(y_for_scaling)
                    y_for_scaling = np.where(np.isnan(y_for_scaling), y_mean, y_for_scaling)
                y_scaled = self.scaler_y.fit_transform(y_for_scaling).flatten()
        else:
            # For transform, handle NaN values before scaling
            X_for_scaling = X_array.copy()
            for col in range(X_for_scaling.shape[1]):
                col_data = X_for_scaling[:, col]
                if np.any(np.isnan(col_data)):
                    # Fill NaN with column mean for scaling
                    col_mean = np.nanmean(col_data)
                    X_for_scaling[:, col] = np.where(np.isnan(col_data), col_mean, col_data)

            X_scaled = self.scaler_X.transform(X_for_scaling)

            if y is not None:
                y_array = np.array(y).reshape(-1, 1)
                y_for_scaling = y_array.copy()
                if np.any(np.isnan(y_for_scaling)):
                    y_mean = np.nanmean(y_for_scaling)
                    y_for_scaling = np.where(np.isnan(y_for_scaling), y_mean, y_for_scaling)
                y_scaled = self.scaler_y.transform(y_for_scaling).flatten()

        # Handle missing values by filling with zeros (will be masked)
        X_filled = np.nan_to_num(X_scaled, nan=0.0)

        if y is not None:
            y_filled = np.nan_to_num(y_scaled, nan=0.0)
            return X_filled, y_filled
        else:
            return X_filled

    def fit(self, X, y, eval_set=None, verbose=False):
        """Fit the SAITS model."""
        self.feature_names_ = getattr(X, 'columns', None)

        # Store original data for mask creation
        X_orig = np.array(X)
        y_orig = np.array(y)

        # Prepare data
        X_prep, y_prep = self._prepare_data(X, y, fit_scalers=True)

        # Create sequences with original data for proper masking
        X_seq, y_seq, combined_mask = self._create_sequences(X_prep, y_prep, X_orig, y_orig)

        # Combine features and target for SAITS (which does imputation)
        combined_data = np.concatenate([X_seq, y_seq.reshape(y_seq.shape[0], y_seq.shape[1], 1)], axis=2)

        # Debug: Check for NaN values
        print(f"Debug: combined_data shape: {combined_data.shape}, n_features: {combined_data.shape[2]}")
        print(f"Debug: NaN count in combined_data: {np.sum(np.isnan(combined_data))}")
        print(f"Debug: NaN count in X_seq: {np.sum(np.isnan(X_seq))}")
        print(f"Debug: NaN count in y_seq: {np.sum(np.isnan(y_seq))}")
        print(f"Debug: NaN count in combined_mask: {np.sum(np.isnan(combined_mask))}")

        # Ensure no NaN values in the data
        combined_data = np.nan_to_num(combined_data, nan=0.0)
        combined_mask = np.nan_to_num(combined_mask, nan=0.0)

        # Initialize model
        n_features = combined_data.shape[2]
        self.model = SAITS(
            n_groups=self.n_groups,
            n_group_inner_layers=self.n_group_inner_layers,
            d_time=self.sequence_length,
            d_feature=n_features,
            d_model=self.d_model,
            d_inner=self.d_inner,
            n_head=self.n_head,
            d_k=self.d_k,
            d_v=self.d_v,
            dropout=self.dropout,
            input_with_mask=self.input_with_mask,
            param_sharing_strategy=self.param_sharing_strategy,
            MIT=self.MIT,
            device=self.device
        ).to(self.device)

        # Setup optimizer
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)

        # Training loop
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.epochs):
            epoch_loss = 0
            n_batches = 0

            # Simple batch processing
            for i in range(0, len(combined_data), self.batch_size):
                batch_data = combined_data[i:i + self.batch_size]
                batch_mask = combined_mask[i:i + self.batch_size]

                # Debug: Check for NaN in batch
                if np.any(np.isnan(batch_data)):
                    print(f"Warning: NaN found in batch_data at epoch {epoch}, batch {i//self.batch_size}")
                    batch_data = np.nan_to_num(batch_data, nan=0.0)

                if np.any(np.isnan(batch_mask)):
                    print(f"Warning: NaN found in batch_mask at epoch {epoch}, batch {i//self.batch_size}")
                    batch_mask = np.nan_to_num(batch_mask, nan=0.0)

                # Convert to tensors
                X_tensor = torch.FloatTensor(batch_data).to(self.device)
                mask_tensor = torch.FloatTensor(batch_mask).to(self.device)

                # Debug: Check for NaN in tensors
                if torch.isnan(X_tensor).any():
                    print(f"Warning: NaN in X_tensor at epoch {epoch}")
                    X_tensor = torch.nan_to_num(X_tensor, nan=0.0)

                if torch.isnan(mask_tensor).any():
                    print(f"Warning: NaN in mask_tensor at epoch {epoch}")
                    mask_tensor = torch.nan_to_num(mask_tensor, nan=0.0)

                # Create missing mask for target (last feature)
                target_mask = mask_tensor[:, :, -1:].clone()

                # Prepare inputs for SAITS
                inputs = {
                    "X": X_tensor,
                    "missing_mask": mask_tensor,
                    "X_holdout": X_tensor,
                    "indicating_mask": target_mask
                }

                optimizer.zero_grad()
                try:
                    outputs = self.model(inputs, stage="train")
                    loss = outputs["reconstruction_loss"]

                    # Check if loss is NaN
                    if torch.isnan(loss):
                        print(f"Warning: NaN loss at epoch {epoch}, skipping batch")
                        continue

                except Exception as e:
                    if "Input contains NaN" in str(e):
                        print(f"NaN error in model forward pass at epoch {epoch}: {e}")
                        # Try to continue with next batch
                        continue
                    else:
                        raise e

                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()
                n_batches += 1

            avg_loss = epoch_loss / n_batches if n_batches > 0 else float('inf')

            if verbose and epoch % 10 == 0:
                print(f"Epoch {epoch}, Loss: {avg_loss:.4f}")

            # Early stopping
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= self.patience:
                    if verbose:
                        print(f"Early stopping at epoch {epoch}")
                    break

        return self

    def predict(self, X):
        """Predict using the trained SAITS model."""
        if self.model is None:
            raise ValueError("Model must be fitted before prediction")

        # Store original data for mask creation
        X_orig = np.array(X)

        # Prepare data
        X_prep = self._prepare_data(X, fit_scalers=False)

        # For prediction, we need to handle the case where we don't have target values
        # We'll create dummy target values and mask them as missing
        dummy_target = np.zeros(len(X_prep))
        dummy_target_orig = np.full(len(X_prep), np.nan)  # Mark all as missing in original

        # Create sequences with proper masking
        X_seq, mask_seq = self._create_sequences(
            np.concatenate([X_prep, dummy_target.reshape(-1, 1)], axis=1),
            original_X=np.concatenate([X_orig, dummy_target_orig.reshape(-1, 1)], axis=1)
        )

        self.model.eval()
        predictions = []

        with torch.no_grad():
            for i in range(0, len(X_seq), self.batch_size):
                batch_data = X_seq[i:i + self.batch_size]
                batch_mask = mask_seq[i:i + self.batch_size]

                # Convert to tensors
                X_tensor = torch.FloatTensor(batch_data).to(self.device)
                mask_tensor = torch.FloatTensor(batch_mask).to(self.device)

                # Handle NaN values in prediction
                if torch.isnan(X_tensor).any():
                    X_tensor = torch.nan_to_num(X_tensor, nan=0.0)

                if torch.isnan(mask_tensor).any():
                    mask_tensor = torch.nan_to_num(mask_tensor, nan=0.0)

                # Prepare inputs for SAITS
                inputs = {
                    "X": X_tensor,
                    "missing_mask": mask_tensor,
                    "X_holdout": X_tensor,
                    "indicating_mask": mask_tensor[:, :, -1:]
                }

                outputs = self.model(inputs, stage="test")
                imputed_data = outputs["imputed_data"]

                # Extract target predictions (last feature)
                target_preds = imputed_data[:, :, -1].cpu().numpy()
                predictions.extend(target_preds[:, -1])  # Take last timestep

        # Convert back to original scale
        predictions = np.array(predictions).reshape(-1, 1)
        predictions_scaled = self.scaler_y.inverse_transform(predictions).flatten()

        # Handle the sequence length offset
        full_predictions = np.full(len(X), np.nan)
        full_predictions[self.sequence_length-1:] = predictions_scaled

        return full_predictions

    def get_params(self, deep=True):
        """Get parameters for this estimator."""
        return {
            'sequence_length': self.sequence_length,
            'n_groups': self.n_groups,
            'n_group_inner_layers': self.n_group_inner_layers,
            'd_model': self.d_model,
            'd_inner': self.d_inner,
            'n_head': self.n_head,
            'd_k': self.d_k,
            'd_v': self.d_v,
            'dropout': self.dropout,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate,
            'patience': self.patience,
            'input_with_mask': self.input_with_mask,
            'param_sharing_strategy': self.param_sharing_strategy,
            'MIT': self.MIT,
            'random_state': self.random_state
        }

    def set_params(self, **params):
        """Set parameters for this estimator."""
        for key, value in params.items():
            setattr(self, key, value)
        return self
