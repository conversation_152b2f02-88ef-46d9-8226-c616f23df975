"""
Integration Script: Apply SAITS NaN Fix to Existing Model
=========================================================

This script shows how to integrate the NaN handling fix into your existing
SAITS model implementation. Copy and paste the relevant sections into your
saits_model.py file.

INTEGRATION STEPS:
1. Add NaN preprocessing function
2. Modify the _prepare_data method
3. Update the fit method
4. Test the integration
"""

import numpy as np
import torch
from sklearn.preprocessing import StandardScaler


def enhanced_nan_preprocessing(data, missing_mask=None):
    """
    Enhanced NaN preprocessing for SAITS - ADD THIS TO YOUR SAITS MODEL

    This function should be added to your SAITSRegressor class.
    """
    # Step 1: Create missing mask if not provided
    if missing_mask is None:
        missing_mask = ~np.isnan(data)

    # Step 2: Replace NaN with zeros
    processed_data = np.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)

    # Step 3: Normalize considering missing values
    normalized_data = processed_data.copy()

    for i in range(data.shape[1]):
        feature_data = data[:, i]
        feature_mask = missing_mask[:, i]

        # Only use valid values for normalization
        valid_data = feature_data[feature_mask]

        if len(valid_data) > 1:  # Need at least 2 values for std
            mean_val = np.mean(valid_data)
            std_val = np.std(valid_data)

            if std_val > 1e-8:  # Avoid division by zero
                # Normalize all values
                normalized_data[:, i] = (processed_data[:, i] - mean_val) / std_val
                # Zero out missing values
                normalized_data[:, i] = normalized_data[:, i] * missing_mask[:, i]
            else:
                # Constant feature - just zero out missing values
                normalized_data[:, i] = processed_data[:, i] * missing_mask[:, i]
        else:
            # Not enough valid data - use zeros
            normalized_data[:, i] = 0.0

    return normalized_data, missing_mask.astype(np.float32)


def modified_prepare_data_method():
    """
    REPLACE YOUR EXISTING _prepare_data METHOD WITH THIS VERSION

    This is the enhanced version that handles NaN values properly.
    """
    code_template = '''
    def _prepare_data(self, X, y=None, fit_scalers=False):
        """Enhanced data preparation with NaN handling."""
        X_array = np.array(X)

        # Store original data for mask creation
        X_original = X_array.copy()

        if fit_scalers:
            # Enhanced preprocessing for training data
            X_processed, X_mask = enhanced_nan_preprocessing(X_array)

            # Fit scaler on processed data
            self.scaler_X = StandardScaler()
            # Only fit on non-zero values (which represent valid data)
            valid_indices = np.any(X_mask, axis=1)  # Rows with at least one valid value
            if np.sum(valid_indices) > 0:
                self.scaler_X.fit(X_processed[valid_indices])
                X_scaled = self.scaler_X.transform(X_processed)
            else:
                # Fallback if no valid data
                self.scaler_X.fit(X_processed)
                X_scaled = X_processed

            # Handle target variable
            if y is not None:
                y_array = np.array(y).reshape(-1, 1)
                y_original = y_array.copy()

                # Preprocess target
                y_processed, y_mask = enhanced_nan_preprocessing(y_array)

                # Fit target scaler
                self.scaler_y = StandardScaler()
                valid_y_indices = y_mask.flatten()
                if np.sum(valid_y_indices) > 0:
                    self.scaler_y.fit(y_processed[valid_y_indices].reshape(-1, 1))
                    y_scaled = self.scaler_y.transform(y_processed).flatten()
                else:
                    self.scaler_y.fit(y_processed)
                    y_scaled = y_processed.flatten()

                return X_scaled, y_scaled, X_mask, y_mask.flatten()
        else:
            # Transform mode
            X_processed, X_mask = enhanced_nan_preprocessing(X_array)
            X_scaled = self.scaler_X.transform(X_processed)

            if y is not None:
                y_array = np.array(y).reshape(-1, 1)
                y_processed, y_mask = enhanced_nan_preprocessing(y_array)
                y_scaled = self.scaler_y.transform(y_processed).flatten()
                return X_scaled, y_scaled, X_mask, y_mask.flatten()
            else:
                return X_scaled, X_mask
    '''
    return code_template


def modified_fit_method():
    """
    REPLACE YOUR EXISTING fit METHOD WITH THIS VERSION

    This version includes proper NaN handling and validation.
    """
    code_template = '''
    def fit(self, X, y, eval_set=None, verbose=False):
        """Enhanced fit method with NaN handling."""
        self.feature_names_ = getattr(X, 'columns', None)

        # Enhanced data preparation
        X_prep, y_prep, X_mask, y_mask = self._prepare_data(X, y, fit_scalers=True)

        # Validation: Check for remaining NaN values
        if np.any(np.isnan(X_prep)) or np.any(np.isnan(y_prep)):
            raise ValueError("NaN values detected after preprocessing - this should not happen")

        # Create sequences with proper masking
        X_seq, y_seq, combined_mask = self._create_sequences(
            X_prep, y_prep, X_mask, y_mask
        )

        # Combine features and target for SAITS
        combined_data = np.concatenate([
            X_seq,
            y_seq.reshape(y_seq.shape[0], y_seq.shape[1], 1)
        ], axis=2)

        # Final NaN check and cleanup
        combined_data = np.nan_to_num(combined_data, nan=0.0, posinf=0.0, neginf=0.0)
        combined_mask = np.nan_to_num(combined_mask, nan=0.0, posinf=1.0, neginf=0.0)

        # Initialize model
        n_features = combined_data.shape[2]
        self.model = SAITS(
            n_groups=self.n_groups,
            n_group_inner_layers=self.n_group_inner_layers,
            d_time=self.sequence_length,
            d_feature=n_features,
            d_model=self.d_model,
            d_inner=self.d_inner,
            n_head=self.n_head,
            d_k=self.d_k,
            d_v=self.d_v,
            dropout=self.dropout,
            input_with_mask=self.input_with_mask,
            param_sharing_strategy=self.param_sharing_strategy,
            MIT=self.MIT,
            device=self.device
        ).to(self.device)

        # Setup optimizer
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)

        # Training loop with enhanced error handling
        self.model.train()
        best_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.epochs):
            epoch_loss = 0
            n_batches = 0

            for i in range(0, len(combined_data), self.batch_size):
                batch_data = combined_data[i:i + self.batch_size]
                batch_mask = combined_mask[i:i + self.batch_size]

                # Final validation before model input
                if np.any(np.isnan(batch_data)) or np.any(np.isnan(batch_mask)):
                    print(f"Warning: NaN detected in batch {i//self.batch_size}, cleaning...")
                    batch_data = np.nan_to_num(batch_data, nan=0.0)
                    batch_mask = np.nan_to_num(batch_mask, nan=0.0)

                # Convert to tensors
                X_tensor = torch.FloatTensor(batch_data).to(self.device)
                mask_tensor = torch.FloatTensor(batch_mask).to(self.device)

                # Tensor validation
                if torch.isnan(X_tensor).any() or torch.isnan(mask_tensor).any():
                    print(f"Warning: NaN in tensors at epoch {epoch}, batch {i//self.batch_size}")
                    X_tensor = torch.nan_to_num(X_tensor, nan=0.0)
                    mask_tensor = torch.nan_to_num(mask_tensor, nan=0.0)

                # Prepare SAITS inputs
                inputs = {
                    "X": X_tensor,
                    "missing_mask": mask_tensor,
                    "X_holdout": X_tensor,
                    "indicating_mask": mask_tensor[:, :, -1:]
                }

                optimizer.zero_grad()

                try:
                    outputs = self.model(inputs, stage="train")
                    loss = outputs["reconstruction_loss"]

                    if torch.isnan(loss):
                        print(f"Warning: NaN loss at epoch {epoch}, skipping batch")
                        continue

                    loss.backward()
                    optimizer.step()

                    epoch_loss += loss.item()
                    n_batches += 1

                except Exception as e:
                    print(f"Error in training step: {e}")
                    continue

            if n_batches > 0:
                avg_loss = epoch_loss / n_batches

                if verbose and epoch % 10 == 0:
                    print(f"Epoch {epoch}, Loss: {avg_loss:.4f}")

                # Early stopping
                if avg_loss < best_loss:
                    best_loss = avg_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= self.patience:
                        if verbose:
                            print(f"Early stopping at epoch {epoch}")
                        break

        return self
    '''
    return code_template


def integration_checklist():
    """
    Print integration checklist for the user.
    """
    print("🔧 SAITS INTEGRATION CHECKLIST")
    print("="*50)
    print("\n✅ STEP 1: Add Enhanced Preprocessing")
    print("   - Copy the 'enhanced_nan_preprocessing' function")
    print("   - Add it to your SAITSRegressor class")

    print("\n✅ STEP 2: Replace _prepare_data Method")
    print("   - Replace your existing _prepare_data method")
    print("   - Use the enhanced version with NaN handling")

    print("\n✅ STEP 3: Replace fit Method")
    print("   - Replace your existing fit method")
    print("   - Use the enhanced version with validation")

    print("\n✅ STEP 4: Test Integration")
    print("   - Run your model with data containing NaN values")
    print("   - Verify no 'Input contains NaN' errors occur")
    print("   - Check training proceeds normally")

    print("\n✅ STEP 5: Optional Enhancements")
    print("   - Add GPU optimization (if CUDA available)")
    print("   - Add performance monitoring")
    print("   - Add comprehensive logging")

    print("\n💡 QUICK TEST:")
    print("   Run: python saits_implementation_guide.py")
    print("   This will validate your implementation")


def main():
    """
    Main integration guide.
    """
    print("🚀 SAITS NaN FIX INTEGRATION GUIDE")
    print("="*50)

    print("\n📋 This script provides the code modifications needed")
    print("to fix the 'Input contains NaN' error in your SAITS model.")

    # Show the integration checklist
    integration_checklist()

    print("\n" + "="*50)
    print("📝 CODE TEMPLATES:")
    print("="*50)

    print("\n1️⃣ ENHANCED PREPROCESSING FUNCTION:")
    print("   (Add this to your SAITSRegressor class)")
    print("-" * 40)
    print("# Copy this function into your saits_model.py:")
    print("# " + enhanced_nan_preprocessing.__doc__.strip())

    print("\n2️⃣ MODIFIED _prepare_data METHOD:")
    print("   (Replace your existing method)")
    print("-" * 40)
    print("# Replace your _prepare_data method with:")
    print("# " + modified_prepare_data_method().strip()[:100] + "...")

    print("\n3️⃣ MODIFIED fit METHOD:")
    print("   (Replace your existing method)")
    print("-" * 40)
    print("# Replace your fit method with:")
    print("# " + modified_fit_method().strip()[:100] + "...")

    print("\n" + "="*50)
    print("🎯 NEXT STEPS:")
    print("="*50)
    print("1. Open your saits_model.py file")
    print("2. Apply the code modifications shown above")
    print("3. Run: python saits_implementation_guide.py")
    print("4. Test with your actual log data")
    print("5. Verify the 'Input contains NaN' error is resolved")

    print("\n✅ After integration, your SAITS model will:")
    print("   - Automatically handle NaN values")
    print("   - Use proper masking mechanism")
    print("   - Provide robust error handling")
    print("   - Work with your existing ML pipeline")


if __name__ == "__main__":
    main()