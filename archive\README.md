# Archive Directory

This directory contains code that is not part of the main ML log prediction functionality but may be useful for reference, development, or future enhancements.

## Directory Structure

### `/tests/`
Contains test scripts and debugging utilities:
- `test_cpu_only.py` - CPU-only SAITS testing
- `test_main_integration.py` - Integration testing for main functionality
- `test_saits.py` - General SAITS testing
- `test_saits_speed.py` - Performance benchmarking for SAITS

### `/demos_and_debug/`
Contains demonstration scripts and debugging tools:
- `demo_saits.py` - Demonstration of SAITS integration with synthetic data
- `debug_saits_dimensions.py` - Debug script for SAITS dimension issues
- `check_gpu.py` - GPU availability and performance checking utility

### `/experimental/`
Contains experimental implementations and alternative approaches:
- `enhanced_saits_data.py` - Enhanced data handling for SAITS (PyPOTS-inspired)
- `enhanced_saits_loss.py` - Enhanced loss functions for SAITS
- `optimized_saits.py` - Alternative optimized SAITS implementation

### `/docs/`
Contains documentation and integration guides:
- `PYPOTS_INTEGRATION_GUIDE.md` - Guide for PyPOTS integration
- `PYPOTS_SAITS_ANALYSIS.md` - Analysis of PyPOTS SAITS implementation
- `SAITS_GPU_INTEGRATION_GUIDE.md` - GPU integration guide for SAITS

### `/temp/`
Contains temporary files and artifacts:
- `catboost_info/` - CatBoost training artifacts and logs

## Main Functionality Files (Remaining in Root)

The following files constitute the core ML log prediction system:
- `main.py` - Main application entry point
- `ml_core.py` - Core ML algorithms and model registry
- `data_handler.py` - Data loading and preprocessing
- `config_handler.py` - Configuration and user interface
- `reporting.py` - Results reporting and visualization
- `saits_model.py` - Production SAITS implementation
- `simple_saits.py` - Simplified SAITS implementation
- `saits_gpu_optimizer.py` - GPU optimization for SAITS
- `requirements.txt` - Python dependencies

## Usage Notes

- Files in this archive are not imported by the main application
- Test files can be run independently for development and debugging
- Experimental implementations may serve as reference for future enhancements
- Documentation files provide context for SAITS integration approaches
