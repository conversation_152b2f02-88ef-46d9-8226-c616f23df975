#!/usr/bin/env python3
"""
Test script for the new all-models plotting functionality.
This script creates synthetic data and tests the plotting functions.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from reporting import create_summary_plots, create_all_models_plots
from ml_core import MODEL_REGISTRY
import warnings
warnings.filterwarnings('ignore')

def create_test_data():
    """Create synthetic well log data for testing."""
    np.random.seed(42)
    
    # Create synthetic data for 2 wells
    wells = ['WELL_A', 'WELL_B']
    n_samples_per_well = 200
    
    all_data = []
    
    for well in wells:
        # Create depth values
        md = np.linspace(1000, 1500, n_samples_per_well)
        
        # Create synthetic log curves with realistic patterns
        gr = 50 + 30 * np.sin(md / 100) + 10 * np.random.normal(0, 1, n_samples_per_well)
        nphi = 0.15 + 0.1 * np.cos(md / 80) + 0.05 * np.random.normal(0, 1, n_samples_per_well)
        rhob = 2.3 + 0.3 * np.sin(md / 120) + 0.1 * np.random.normal(0, 1, n_samples_per_well)
        dt = 100 + 20 * np.cos(md / 90) + 5 * np.random.normal(0, 1, n_samples_per_well)
        
        # Create DataFrame for this well
        well_data = pd.DataFrame({
            'WELL': well,
            'MD': md,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'DT': dt
        })
        
        all_data.append(well_data)
    
    # Combine all wells
    df = pd.concat(all_data, ignore_index=True)
    
    # Introduce some missing values in the target log
    missing_indices = np.random.choice(df.index, size=int(0.3 * len(df)), replace=False)
    df.loc[missing_indices, 'DT'] = np.nan
    
    return df

def create_mock_model_results(df, target_col='DT'):
    """Create mock model results for testing plotting."""
    
    # Create mock evaluations
    evaluations = [
        {'model_name': 'XGBoost', 'mae': 8.234, 'r2': 0.856, 'rmse': 12.45, 'composite_score': 6.123},
        {'model_name': 'LightGBM', 'mae': 8.567, 'r2': 0.842, 'rmse': 13.12, 'composite_score': 6.445},
        {'model_name': 'CatBoost', 'mae': 9.123, 'r2': 0.831, 'rmse': 13.78, 'composite_score': 6.892},
    ]
    
    # Add SAITS if available
    try:
        from saits_model import SAITSRegressor
        evaluations.append({
            'model_name': 'SAITS', 'mae': 7.891, 'r2': 0.867, 'rmse': 11.98, 'composite_score': 5.834
        })
    except ImportError:
        print("SAITS not available for testing")
    
    try:
        from simple_saits import SimpleSAITSRegressor
        evaluations.append({
            'model_name': 'Simple SAITS', 'mae': 8.012, 'r2': 0.863, 'rmse': 12.23, 'composite_score': 5.967
        })
    except ImportError:
        print("Simple SAITS not available for testing")
    
    # Sort by composite score
    evaluations.sort(key=lambda x: x['composite_score'])
    
    # Create mock trained models (simplified for testing)
    class MockModel:
        def __init__(self, name, noise_factor=1.0):
            self.name = name
            self.noise_factor = noise_factor
        
        def predict(self, X):
            # Simple mock prediction: use DT column if available, otherwise create synthetic
            if 'DT' in X.columns:
                base_pred = X['DT'].fillna(X['DT'].mean())
            else:
                # Create synthetic predictions based on other features
                base_pred = 100 + 0.5 * X['GR'] - 200 * X['NPHI'] + 20 * X['RHOB']
            
            # Add model-specific noise
            noise = np.random.normal(0, self.noise_factor, len(base_pred))
            return base_pred + noise
    
    trained_models = {}
    for eval_data in evaluations:
        model_name = eval_data['model_name']
        # Different noise levels for different models
        noise_factor = 2.0 + eval_data['composite_score'] * 0.5
        trained_models[model_name] = MockModel(model_name, noise_factor)
    
    # Create imputed column using best model
    best_model_name = evaluations[0]['model_name']
    feature_cols = ['GR', 'NPHI', 'RHOB', 'MD']
    X_pred = df[feature_cols].fillna(df[feature_cols].mean())
    
    best_predictions = trained_models[best_model_name].predict(X_pred)
    df[f'{target_col}_imputed'] = df[target_col].fillna(best_predictions)
    
    # Create model results structure
    model_results = {
        'target': target_col,
        'evaluations': evaluations,
        'best_model_name': best_model_name,
        'trained_models': trained_models
    }
    
    return df, model_results

def test_plotting_functions():
    """Test both plotting functions."""
    print("🧪 Testing All Models Plotting Functionality")
    print("=" * 60)
    
    # Create test data
    print("📊 Creating synthetic test data...")
    df = create_test_data()
    print(f"✅ Created data with {len(df)} samples for wells: {df['WELL'].unique()}")
    
    # Create mock model results
    print("🤖 Creating mock model results...")
    df_with_results, model_results = create_mock_model_results(df)
    print(f"✅ Created results for {len(model_results['trained_models'])} models")
    
    # Create well configuration
    cfg = {
        'mode': 'mixed',
        'prediction_wells': df['WELL'].unique(),
        'training_wells': df['WELL'].unique()
    }
    
    # Test original plotting function
    print("\n📈 Testing original summary plots (best model only)...")
    try:
        create_summary_plots(df_with_results, model_results, cfg)
        print("✅ Original plotting function works correctly")
    except Exception as e:
        print(f"❌ Original plotting function failed: {e}")
    
    # Test new all-models plotting function (separate figures)
    print("\n📈 Testing new all-models plotting function (separate figures)...")
    try:
        create_all_models_plots(df_with_results, model_results, cfg)
        print("✅ All-models plotting function (separate figures) works correctly")
    except Exception as e:
        print(f"❌ All-models plotting function (separate figures) failed: {e}")

    print("\n🎉 Testing completed!")
    print("\nModel Performance Summary:")
    for i, eval_data in enumerate(model_results['evaluations'], 1):
        print(f"  {i}. {eval_data['model_name']}: MAE={eval_data['mae']:.3f}, R²={eval_data['r2']:.3f}")

    print("\n📊 Key Features Demonstrated:")
    print("✅ Original summary plot (best model only)")
    print("✅ Individual model plots (each model in separate figure)")
    print("✅ Performance comparison charts")
    print("✅ Model ranking and metrics")
    print("\n💡 Each model now gets its own dedicated figure for better visibility!")

if __name__ == "__main__":
    test_plotting_functions()
